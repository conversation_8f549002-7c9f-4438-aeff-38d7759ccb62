using FairyGUI;
using UnityEngine;

/// <summary>
/// 折扣礼品弹窗面板
/// </summary>
public class DiscountGiftPanel : Panel
{
    public static int CloseType_Reward = 1;

    private GList listReward;
    private ItemVo[] currentRewards;

    public DiscountGiftPanel()
    {
        packName = "Battle";
        compName = "DiscountGiftPanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        var txtDesc = contentPane.GetChild("txtDesc").asTextField;
        var rewardCount = ConfigSetting.discountGiftRewardCount;
        txtDesc.text = LangUtil.GetText("txtRandomItems", rewardCount);

        // 获取奖励列表组件
        listReward = contentPane.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateRewardItem;

        // 获取当前关卡的奖励
        currentRewards = DiscountGiftRewardManager.GetCurrentLevelRewards();

        // 设置列表数据
        listReward.data = currentRewards;
        listReward.numItems = currentRewards.Length;
    }


    /// <summary>
    /// 更新奖励列表项
    /// </summary>
    /// <param name="index">索引</param>
    /// <param name="item">列表项对象</param>
    private void UpdateRewardItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var lblItemName = item.asCom.GetChild("lblItemName").asTextField;

        var reward = currentRewards[index];
        var itemInfo = ConfigItem.GetData(reward.itemId);

        icon.url = itemInfo.IconUrl;
        lblItemName.text = $"{itemInfo.name}+{reward.count}";
    }

    /// <summary>
    /// 获取当前显示的奖励列表
    /// </summary>
    /// <returns>奖励列表</returns>
    public ItemVo[] GetCurrentRewards()
    {
        return currentRewards;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnFree":
                OnFreeButtonClick();
                break;
            case "btnClose":
                Hide();
                break;
        }
    }

    private void OnFreeButtonClick()
    {
        Hide(CloseType_Reward);
    }
}
