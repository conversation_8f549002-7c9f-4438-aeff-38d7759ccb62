fileFormatVersion: 2
guid: 73c2fc41934ef7a4babab754b033c426
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme3_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd3328e2a2d6e124994c4d1cfc760a4b
      internalID: -2095632061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a97470397b862da4eb4ff420b36bbb97
      internalID: -432849112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d9e91b0a36981fc4a96798a8f5bc845a
      internalID: -1878997129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2d80c8a17513cc841a7b3b434dd48ddf
      internalID: 720546380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ecff243bb6b44ee4b920390d9ab73699
      internalID: 1923560787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5a0d4fcb90adca84fb0f8db9e7d2a8a8
      internalID: 815320565
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3e6ded9013aaca24693e1b34c334e503
      internalID: 1757126964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 618ebd947124bde4cba85ba293a9f3b6
      internalID: 312942092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9df4575c2659495438c984eea8eb4428
      internalID: 869260242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16e0dabff593e0d4697a1c6834c2efdf
      internalID: 1541659695
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b489ffad5b1a3be4a97352ef04318917
      internalID: -115667384
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e64b8c9e29baa0c47a8ded227324e9ac
      internalID: 1151505600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 524a5223239e49049a269dfbca695078
      internalID: -1609483734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a437b8a3d032c6468aa6c8cd579c528
      internalID: -791042844
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bbebd955400faf34cb652e8ad77019d2
      internalID: -524050854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d6cf557735c9825488fdef423f33bbed
      internalID: 1680337057
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7758b54b97f643b4d8d09997e4c22624
      internalID: -305774379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fbdc6f3f52d86514b9b7b31734be179c
      internalID: 1161417730
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f07c39bde51f9f648a0b7590418af6ea
      internalID: -1366168439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b86047d45bf11f48bd9f8ade70739e4
      internalID: 1413230651
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a9da59213e67eaf4486638c7226f5c72
      internalID: -311961721
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8453db3755dcf3e42855380ebfe6ab54
      internalID: -478140060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71968e71d1d2b464dbd7d0da6772893f
      internalID: -838087087
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0fb5e104e382fb148bf146de531eb466
      internalID: -1660303997
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 585dc467d31af294295c689b722bba71
      internalID: 962112865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f1804b1b7be82b4bbd33892ad239759
      internalID: 470055787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33532e2a600fd984799f199f9abac8f8
      internalID: -781976597
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ce55680813477bd40ab24e1dd46f7495
      internalID: 974635896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 319a381df50f7624dabb41854d20b23f
      internalID: -887634891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38293ae3382f5fc4f9bd252b62b362b8
      internalID: -1738559511
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd9a2d019efd93544b3f48a93a0aafce
      internalID: -945746030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b0da62b855b965d4fac03a99be6da84a
      internalID: 2033710194
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 84dd3d79347b2ce4288886dd160e468c
      internalID: 81533672
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0514d472c34ffb84da9a81588bf37ddb
      internalID: 75135845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dfda406ac00f18641bd2b6cf36551a60
      internalID: 2114803843
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme3_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a56957153ffcbb24384add88d9794b4d
      internalID: 581650059
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: f2bce5a9cb0ea854ab22fb51deb79949
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme3_28: -887634891
      theme3_30: -945746030
      theme3_27: 974635896
      theme3_5: 815320565
      theme3_10: -115667384
      theme3_17: 1161417730
      theme3_34: 2114803843
      theme3_8: 869260242
      theme3_19: 1413230651
      theme3_12: -1609483734
      theme3_33: 75135845
      theme3_32: 81533672
      theme3_0: -2095632061
      theme3_20: -311961721
      theme3_31: 2033710194
      theme3_35: 581650059
      theme3_4: 1923560787
      theme3_22: -838087087
      theme3_6: 1757126964
      theme3_15: 1680337057
      theme3_14: -524050854
      theme3_1: -432849112
      theme3_29: -1738559511
      theme3_2: -1878997129
      theme3_11: 1151505600
      theme3_7: 312942092
      theme3_13: -791042844
      theme3_21: -478140060
      theme3_9: 1541659695
      theme3_18: -1366168439
      theme3_16: -305774379
      theme3_26: -781976597
      theme3_24: 962112865
      theme3_23: -1660303997
      theme3_3: 720546380
      theme3_25: 470055787
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
