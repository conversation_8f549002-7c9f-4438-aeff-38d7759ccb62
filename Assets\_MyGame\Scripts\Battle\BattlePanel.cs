using System;
using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

class BattlePanel : Panel
{
    public BattlePanel()
    {
        packName = "Battle";
        compName = "BattlePanel";
    }

    private bool isGameWin;
    private ItemButton btnCleanUp;
    private ItemButton btnShuffle;
    private ItemButton btnTips;

    private bool isGameDone;
    private GTextField txtLeftCount;

    private GTextField txtTime;
    private GImage txtTimeBg;
    // private GList listLife;
    private GButton btnArrow;
    private int gateEntry;

    private Controller solutionCtrl;
    private Transition solutionTran;
    private Transition refreshTran;
    private bool isNoSolution;
    private GProgressBar prgGate;
    private GComponent itemTips;
    private GTextField lblItemDesc;
    private Transition itemTipsEnterTween;
    private GButton btnGm;
    // private bool isUsedPrompt;
    private GTextField lblNeighborCount;
    private UIItemPool uIItemPool = new();

    // 折扣礼品相关
    private BtnDiscountGift discountGiftBalloon;
    private bool hasShownDiscountGift = false;

    // 缓存进度信息
    private float cachedGameProgress = 0f;
    protected override void DoInitialize()
    {
        isGameWin = false;
        isNoSolution = false;

        solutionCtrl = contentPane.GetController("c1");
        solutionTran = contentPane.GetTransition("t0");
        refreshTran = contentPane.GetTransition("t1");

        prgGate = contentPane.GetChild("prgGate").asProgress;
        var btnGateBox = prgGate.GetChild("btnGateBox").asButton;

        itemTips = contentPane.GetChild("itemTips").asCom;
        itemTipsEnterTween = itemTips.GetTransition("enterTween");
        lblItemDesc = itemTips.GetChild("lblItemDesc").asTextField;

        var lblGateVer = contentPane.GetChild("lblGateVer").asTextField;
        lblGateVer.text = GameGlobal.Channel;

        var boxHead = contentPane.GetChild("boxHead");
        FixUIOffset.FixTop(boxHead);
        // contentPane.fairyBatching = false;

        // guideComp = contentPane.GetChild("guideComp").asCom;
        // maskHolder = guideComp.GetChild("maskHolder").asGraph;
        // guideComp.mask = maskHolder.displayObject;
        // guideComp.reversedMask = true;

        btnCleanUp = new ItemButton(ItemId.ItemCleanUp, contentPane.GetChild("btnCleanUp"));
        btnShuffle = new ItemButton(ItemId.ItemShuffle, contentPane.GetChild("btnShuffle"));
        btnTips = new ItemButton(ItemId.ItemPrompt, contentPane.GetChild("btnTips"));

        btnGm = contentPane.GetChild("btnGm").asButton;
        btnGm.visible = ConfigSetting.isShowGmTool;

        // listLife = contentPane.GetChild("listLife").asList;
        btnArrow = contentPane.GetChild("btnArrow").asButton;
        lblNeighborCount = contentPane.GetChild("lblNeighborCount").asTextField;

        var leftTimeBar = contentPane.GetChild("leftTimeBar").asCom;
        txtTime = leftTimeBar.GetChild("txtTime").asTextField;
        txtTimeBg = leftTimeBar.GetChild("txtTimeBg").asImage;
        var txtLevel = contentPane.GetChild("txtLevel").asTextField;
        if (GameGlobal.BattleType == BattleType.DailyChallenge)
        {
            btnGateBox.visible = true;
            txtLevel.text = SystemFacade.DailyChallengeSystem.GetTodayChallengeDate();
        }
        else
        {
            btnGateBox.visible = GameGlobal.EnterLevel > 1;
            txtLevel.text = LangUtil.GetText("txtGate", GameGlobal.EnterLevel);
        }
        InitItem(false);

        hasShownDiscountGift = HasShownDiscountGiftForCurrentLevel();
    }

    private BattleHandler battleHandler;
    public void StartGame(BattleHandler battleHandler)
    {
        // isUsedPrompt = false;
        this.battleHandler = battleHandler;
        OnTileMatched(false);
    }

    public void OnSceneTileInit()
    {
        if (ConfigSetting.isShowGmTool && BattleScene.Inst != null)
        {
            lblNeighborCount.text = LangUtil.GetText("txtNeighborCount", BattleScene.Inst.GetNeighborPairCount());
        }

        UpdateGateProgress();
    }

    public void UpdateLife(int lifeCount)
    {
        // if (lifeCount < listLife.numItems)
        // {
        //     var cell = listLife.GetChildAt(lifeCount);
        //     cell.TweenScale(Vector2.one * 1.5f, 0.3f);
        //     cell.TweenFade(0, 0.3f);
        // }
        // else
        // {
        //     listLife.numItems = lifeCount;
        //     for (int i = 0; i < listLife.numItems; i++)
        //     {
        //         var cell = listLife.GetChildAt(i);
        //         cell.scale = Vector2.one;
        //         cell.alpha = 1;
        //     }
        // }
    }


    /// <summary>
    /// 显示剩余时间
    /// </summary>
    /// <param name="show"></param>
    internal void ShowLeftTime(bool show)
    {
        txtTime.visible = show;
        txtTimeBg.visible = show;
    }

    /// <summary>
    /// 更新剩余时间
    /// </summary>
    /// <param name="time">剩余时间</param>
    internal void UpdateLeftTime(float time)
    {
        txtTime.text = DateUtil.MinuteSecond(time);
    }

    private int _leftIdiomCount;
    private int _totalIdiomCount;
    /// <summary>
    /// 更新剩余数量
    /// </summary>
    /// <param name="count"></param>
    internal void UpdateLeftCount(int count, int totalIdiomCount)
    {
        _leftIdiomCount = count;
        _totalIdiomCount = totalIdiomCount;
        txtLeftCount.text = count.ToString();
    }


    public void Pause()
    {
        battleHandler.Pause();
    }

    public void Resume()
    {
        battleHandler.Resume();
    }

    public void GameStart(bool failAndContinue = false)
    {
        isGameDone = false;
        isNoSolution = false;
        InitItem(failAndContinue);
    }

    public void Revival()
    {
        isGameDone = false;
    }

    public void GameWin()
    {
        isGameDone = true;
        Pause();
    }

    public void GameOver()
    {
        isGameDone = true;
        Pause();
        ShowResult(FailReason.LifeEmpty);
    }

    public void ShowResult(FailReason reason)
    {
        Create((ResultFailPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                if (type == ResultFailPanel.CloseType_Continue)
                {
                    Resume();
                }
            };
            panel.SetData(reason, _leftIdiomCount, _totalIdiomCount, false);
        });
    }

    private void InitItem(bool failAndContinue)
    {
        UpdateItemCount();
    }

    private void UpdateItemCount()
    {
        btnCleanUp.UpdateItemCount();
        btnShuffle.UpdateItemCount();
        btnTips.UpdateItemCount();
    }

    protected override void OnMouseClick(string targetName)
    {
        if (isGameDone)
            return;

        switch (targetName)
        {
            case "btnSetting":
                Create((SettingPanel panel) =>
                {
                    panel.SetData(SettingPanel.SettingViewMode.Pause);
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;

                        if (type == 0)
                        {
                            OnResumeGame();
                        }
                    };
                }, cache: true);
                break;
            case "btnCleanUp":
                UseItem(ItemId.ItemCleanUp);
                break;
            case "btnShuffle":
                UseItem(ItemId.ItemShuffle);
                break;
            // case "btnAnyClick":
            //     UseItem(ItemId.ItemAnyClick);
            //     break;
            case "btnTips":
                UseItem(ItemId.ItemPrompt);
                break;
            case "btnRule":
                Create<GameRulePanel>();
                break;
            case "btnArrow":
                if (gateEntry == 0)
                    return;
                TipMgr.ShowTip(LangUtil.GetText("txtGateEntry" + gateEntry));
                break;
            case "btnGateBox":
                ShowGateBoxTips();
                break;
            case "btnGm":
                ShowGmTool();
                break;
        }
    }

    private GmTool gmTool;
    private void ShowGmTool()
    {
        if (gmTool == null)
        {
            gmTool = new GmTool();
            gmTool.OnPassGate = OnGmPassGate;
        }

        if (!gmTool.IsShow)
        {
            gmTool.Show(contentPane, btnGm.TransformPoint(new Vector2(0, 80), contentPane));
        }
        else
        {
            gmTool.Hide();
        }
    }

    private void OnGmPassGate()
    {
        battleHandler?.OnGameDone(true);
    }

    public GLoader GetGravityArrow()
    {
        return contentPane.GetChild("imgArrow").asLoader;
    }

    private void ShowGateBoxTips()
    {
        Create((GateBoxTipsPanel panel) =>
        {

        });
    }

    private void OnResumeGame()
    {
        Resume();
    }

    internal void OnHasSolution(bool hasSolution)
    {
        this.isNoSolution = !hasSolution;
        solutionCtrl.selectedIndex = hasSolution ? 0 : 1;
        if (hasSolution)
        {
            refreshTran.Stop();
            var shuffleObj = btnShuffle.GetGObject();
            shuffleObj.TweenScale(Vector2.one, 0.1f);
            solutionTran.PlayReverse();
        }
        else
        {
            solutionTran.Play();
            refreshTran.Play(-1, 0, null);
        }
    }

    #region 使用道具
    private void UseItem(int itemId)
    {
        HideItemTips();
        if (SystemFacade.ItemSystem.HasEnoughItem(itemId))
        {
            var useSuccess = battleHandler.UseItem(itemId);
            if (useSuccess)
            {
                // if (itemId == ItemId.ItemPrompt)
                // {
                //     isUsedPrompt = true;
                // }
                SystemFacade.ItemSystem.UseItem(itemId);
                Report.Instance.ReportUseItem(itemId, false);

                UpdateItemCount();

                if (isNoSolution && itemId == ItemId.ItemShuffle)
                {
                    OnHasSolution(true);
                }
            }
        }
        else
        {
            // ShowBuyItemPanel(itemId);

            string adType = string.Empty;
            int itemCount = 1;
            if (itemId == ItemId.ItemCleanUp) adType = AdType.buyItemCleanUp;
            else if (itemId == ItemId.ItemShuffle) adType = AdType.buyItemShuffle;
            else if (itemId == ItemId.ItemPrompt)
            {
                itemCount = 3;
                adType = AdType.buyItemPrompt;
            }

            Pause();
            Platform.Instance.ShowVideoAd(adType, () =>
            {
                if (contentPane == null || contentPane.isDisposed)
                    return;
                Report.Instance.ReportUseItem(itemId, true);
                SystemFacade.ItemSystem.AddItem(itemId, itemCount);

                Resume();
                GetItemRewards(new ItemVo[] { new(itemId, itemCount) });
            });
        }
    }
    #endregion

    private void ShowItemTips(object obj)
    {
        if (contentPane == null || contentPane.isDisposed)
            return;

        var isPromptTip = UnityEngine.Random.value > 0.5f;
        lblItemDesc.text = isPromptTip ? LangUtil.GetText("txtPromptDesc") : LangUtil.GetText("txtShuffleDesc");
        Vector2 tipPos;
        ItemButton targetBtn;
        if (isPromptTip)
        {
            tipPos = btnTips.GetGObject().xy;
            targetBtn = btnTips;
        }
        else
        {
            tipPos = btnShuffle.GetGObject().xy;
            targetBtn = btnShuffle;
        }
        itemTips.xy = tipPos;

        // 播放按钮的EnterTween动画
        var btnEnterTween = targetBtn.GetGObject().asCom.GetTransition("EnterTween");
        if (btnEnterTween != null)
        {
            btnEnterTween.Play();
        }

        itemTips.visible = true;
        itemTipsEnterTween.Play(onComplete: () =>
        {
            HideItemTips();
        });
    }

    private void HideItemTips()
    {
        if (contentPane == null || contentPane.isDisposed)
            return;
        itemTips.visible = false;

        StartItemTipTime();
    }

    private ItemButton GetButtonByItem(int itemId)
    {
        switch (itemId)
        {
            case ItemId.ItemCleanUp:
                return btnCleanUp;
            case ItemId.ItemShuffle:
                return btnShuffle;
            case ItemId.ItemPrompt:
                return btnTips;
        }
        return null;
    }

    private void GetItemRewards(ItemVo[] items)
    {
        var flyTargets = new GObject[items.Length];
        for (int i = 0; i < items.Length; i++)
        {
            if (items[i] == null) continue;
            flyTargets[i] = GetButtonByItem(items[i].itemId).GetGObject();
        }

        TipMgr.ShowRewardsWithMulTargets(items,
            flyTargets: flyTargets,
            autoHideTime: 0.5f,
            OnClosed: () =>
            {
                if (contentPane == null || contentPane.isDisposed)
                    return;

                if (!refreshTran.playing)
                {
                    for (int i = 0; i < flyTargets.Length; i++)
                    {
                        var btnItem = flyTargets[i];
                        if (btnItem == null) continue;
                        btnItem.scale = Vector2.one * 0.9f;
                        btnItem.TweenScale(Vector2.one, 0.3f).SetEase(EaseType.BackOut);
                    }
                }

                UpdateItemCount();
            });
    }

    private void ShowBuyItemPanel(int itemId)
    {
        Pause();

        var itemCount = itemId == ItemId.ItemPrompt ? 3 : 1;
        Create((BuyItemPanel panel) =>
        {
            panel.OnBuySuccess = (BuyType type) =>
            {
                if (contentPane.isDisposed)
                    return;

                GetItemRewards(new ItemVo[] { new(itemId, itemCount) });
            };

            panel.OnClosed = (type) =>
            {
                if (contentPane.isDisposed)
                    return;
                Resume();
            };
            panel.SetData(itemId, itemCount);
        });
    }

    internal void FlyGravityArrow(int gateEntry, Vector2 startPos)
    {
        if (btnArrow == null || btnArrow.isDisposed)
            return;
        this.gateEntry = gateEntry;

        btnArrow.icon = PathUtil.GetGateEntry(gateEntry.GetHashCode());
        var targetPos = btnArrow.xy;

        btnArrow.xy = startPos;
        btnArrow.scale = Vector2.one;
        btnArrow.visible = true;
        btnArrow.TweenMove(targetPos, 0.3f).SetDelay(0.2f).SetEase(EaseType.QuadOut);
        btnArrow.TweenScale(Vector2.one * 0.36f, 0.3f).SetDelay(0.2f);
    }

    #region 消除道具表现
    internal void PlayHammers(Vector3 scenePos)
    {
        PlayHammer(scenePos);
        // PlayHammer(scenePos2);
    }

    internal void PlayHammer(Vector3 scenePos)
    {
        var screenPos = BattleScene.Inst.mainCamera.WorldToScreenPoint(scenePos);
        var guiPos = GRoot.inst.GlobalToLocal(new Vector2(screenPos.x, Screen.height - screenPos.y));
        var hammerEff = uIItemPool.CreateObject("Battle", "EffectHammer", contentPane).asCom;
        var effect = hammerEff.GetChild("effect").asLoader3D;
        hammerEff.xy = guiPos;
        hammerEff.pivotAsAnchor = true;
        hammerEff.pivot = Vector2.one * 0.5f;
        hammerEff.touchable = false;
        contentPane.AddChild(hammerEff);
        if (effect != null)
        {
            effect.spineAnimation.Play("animation", (obj) =>
            {
                if (contentPane == null || contentPane.isDisposed)
                    return;
                uIItemPool.Release(hammerEff);
            });
            // Timers.inst.Add(0.3f, 1, (obj) =>
            // {
            if (contentPane == null || contentPane.isDisposed)
                return;
            SoundManager.PlayEffect(SoundPath.sfx_useItem_Remove);
            // });
        }
    }
    #endregion

    #region Combo表现
    internal void PlayComboEffect(Vector3 scenePos, int comboCount)
    {
        if (comboCount <= 0 || contentPane.isDisposed)
            return;

        var comboEffect = uIItemPool.CreateObject("Battle", "ComboEffect", contentPane).asCom;
        var screenPos = BattleScene.Inst.mainCamera.WorldToScreenPoint(scenePos);
        var guiPos = GRoot.inst.GlobalToLocal(new Vector2(screenPos.x, Screen.height - screenPos.y));
        comboEffect.xy = guiPos;
        comboEffect.alpha = 1;
        comboEffect.touchable = false;

        contentPane.AddChild(comboEffect);
        var icon = comboEffect.GetChild("icon").asLoader;
        var comboIndex = ((comboCount - 1) % 6) + 1;//1~6
        icon.url = GetCurPackRes($"combo{comboIndex}");

        // 只有当combo索引大于等于配置的阈值时才播放音效
        // if (comboIndex >= ConfigSetting.comboSoundThreshold)
        // {
        //     SoundManager.PlayEffect(SoundPath.GetComboSound(comboIndex));
        // }

        // 向上缓动并淡出
        comboEffect.TweenMoveY(comboEffect.y - 60, 1f);
        comboEffect.TweenFade(0, 1.2f).OnComplete(() =>
        {
            if (contentPane == null || contentPane.isDisposed)
                return;
            uIItemPool.Release(comboEffect);
        });
    }
    #endregion

    protected override void OnHide()
    {
        Timers.inst.Remove(ShowItemTips);
        Timers.inst.Remove(ShowDoubleClickTip);
        Timers.inst.Remove(CheckDiscountGiftCondition);
        CleanupDiscountGiftBalloon();
        uIItemPool.Clear();
    }
    private void UpdateGateProgress()
    {
        var curCount = BattleScene.Inst.GetRemainingTileCount();
        var totalCount = BattleScene.Inst.GetTotalTileCount();
        prgGate.max = totalCount;
        prgGate.TweenValue(totalCount - curCount, 0.2f);

        // 更新缓存的进度信息
        if (totalCount > 0)
        {
            cachedGameProgress = (totalCount - curCount) / (float)totalCount * 100f;
        }
        else
        {
            cachedGameProgress = 0f;
        }
    }

    internal void OnTileMatched(bool isMatchSuccessful)
    {
        if (isMatchSuccessful)
        {
            UpdateGateProgress();
            StartItemTipTime();
            StartDoubleClickTipTime();
            StartDiscountGiftTime();
        }
    }

    private bool alreadyShowDoubleClickTip = false;
    private void StartDoubleClickTipTime()
    {
        if (GameGlobal.EnterLevel == 2 && !alreadyShowDoubleClickTip)
        {
            Timers.inst.Remove(ShowDoubleClickTip);
            Timers.inst.Add(ConfigSetting.idleTimeForDoubleClickTip, 1, ShowDoubleClickTip);
        }
    }

    private void ShowDoubleClickTip(object obj)
    {
        alreadyShowDoubleClickTip = true;
        Create<ClickGuideTips>();
    }

    private void StartItemTipTime()
    {
        Timers.inst.Remove(ShowItemTips);
        Timers.inst.Add(ConfigSetting.idleTimeForItemTip, 1, ShowItemTips);
    }

    #region  折扣礼包

    private void StartDiscountGiftTime()
    {
        if (GameGlobal.BattleType != BattleType.Normal)
            return;
        // Timers.inst.Remove(CheckDiscountGiftCondition);
        // Timers.inst.Add(ConfigSetting.discountGiftIdleTime, 1, CheckDiscountGiftCondition);

        CheckDiscountGiftCondition(null);
    }

    /// <summary>
    /// 检查当前关卡是否已经显示过折扣礼品
    /// </summary>
    private bool HasShownDiscountGiftForCurrentLevel()
    {
        // 获取上次显示折扣礼品的关卡号和战斗类型
        int lastShownLevel = StorageMgr.DiscountGiftLastShownLevel;
        int lastShownBattleType = StorageMgr.DiscountGiftLastShownBattleType;

        // 如果当前关卡和战斗类型与上次显示的相同，则表示已显示过
        return lastShownLevel == GameGlobal.EnterLevel && lastShownBattleType == (int)GameGlobal.BattleType;
    }

    /// <summary>
    /// 标记当前关卡已显示过折扣礼品
    /// </summary>
    private void MarkDiscountGiftShownForCurrentLevel()
    {
        // 保存当前关卡号和战斗类型
        StorageMgr.DiscountGiftLastShownLevel = GameGlobal.EnterLevel;
        StorageMgr.DiscountGiftLastShownBattleType = (int)GameGlobal.BattleType;
    }

    /// <summary>
    /// 检测折扣礼品显示条件
    /// </summary>
    private void CheckDiscountGiftCondition(object obj)
    {
        // 如果已经显示过或者游戏已结束，不再检测
        if (hasShownDiscountGift || isGameDone || discountGiftBalloon != null)
            return;

        // 检查配置是否有效
        if (ConfigSetting.discountGiftShowTime <= 0 || ConfigSetting.discountGiftRewardCount <= 0)
            return;

        var battleScene = BattleScene.Inst;
        if (battleScene == null)
            return;

        // 检查进度条件：游戏进度大于配置的阈值
        // if (cachedGameProgress < ConfigSetting.discountGiftProgressThreshold)
        //     return;

        // 满足条件，显示折扣礼品气球
        ShowDiscountGiftBalloon();
    }

    /// <summary>
    /// 显示折扣礼品气球
    /// </summary>
    private void ShowDiscountGiftBalloon()
    {
        hasShownDiscountGift = true;

        // 创建折扣礼品气球UI组件
        var balloonObj = UIPackage.CreateObject("Battle", "BtnDiscountGift").asCom;

        // 创建包装器
        discountGiftBalloon = new BtnDiscountGift(balloonObj);

        // 添加到界面
        contentPane.AddChild(balloonObj);

        // 设置事件回调
        discountGiftBalloon.OnClicked = OnDiscountGiftClicked;
        discountGiftBalloon.OnTimeUp = OnDiscountGiftTimeUp;

        // 初始化气球
        discountGiftBalloon.Initialize();
    }

    /// <summary>
    /// 折扣礼品气球被点击
    /// </summary>
    private void OnDiscountGiftClicked()
    {
        // 显示折扣礼品弹窗
        Create((DiscountGiftPanel panel) =>
        {
            Pause();
            panel.OnClosed = (type) =>
            {
                Resume();
                if (type == DiscountGiftPanel.CloseType_Reward)
                {
                    Platform.Instance.ShowVideoAd(AdType.discountGift, () =>
                    {
                        if (contentPane == null || contentPane.isDisposed)
                            return;

                        MarkDiscountGiftShownForCurrentLevel();

                        // 获取面板中显示的奖励并发放给玩家
                        var rewards = panel.GetCurrentRewards();
                        foreach (var reward in rewards)
                        {
                            SystemFacade.ItemSystem.AddItem(reward.itemId, reward.count);
                        }

                        GetItemRewards(rewards);
                    });
                }
            };
        });
    }

    /// <summary>
    /// 折扣礼品气球时间到
    /// </summary>
    private void OnDiscountGiftTimeUp()
    {
        // 清理气球
        CleanupDiscountGiftBalloon();
    }

    /// <summary>
    /// 清理折扣礼品气球
    /// </summary>
    private void CleanupDiscountGiftBalloon()
    {
        if (discountGiftBalloon != null)
        {
            discountGiftBalloon.Destroy();
            discountGiftBalloon = null;
        }
    }
    #endregion
}
