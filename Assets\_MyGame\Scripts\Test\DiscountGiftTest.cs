using UnityEngine;

/// <summary>
/// 折扣礼品功能测试脚本
/// 用于验证折扣礼品奖励管理器的功能
/// </summary>
public class DiscountGiftTest : MonoBehaviour
{
    [ContextMenu("测试获取当前关卡奖励")]
    public void TestGetCurrentLevelRewards()
    {
        Debug.Log("=== 测试获取当前关卡奖励 ===");
        
        var rewards = DiscountGiftRewardManager.GetCurrentLevelRewards();
        
        if (rewards != null && rewards.Length > 0)
        {
            Debug.Log($"获取到 {rewards.Length} 个奖励:");
            for (int i = 0; i < rewards.Length; i++)
            {
                var reward = rewards[i];
                var itemInfo = ConfigItem.GetData(reward.itemId);
                Debug.Log($"  奖励 {i + 1}: {itemInfo.name} x{reward.count} (ID: {reward.itemId})");
            }
        }
        else
        {
            Debug.LogError("未获取到任何奖励！");
        }
    }
    
    [ContextMenu("测试重复获取奖励")]
    public void TestGetRewardsMultipleTimes()
    {
        Debug.Log("=== 测试重复获取奖励（应该返回相同结果） ===");
        
        var rewards1 = DiscountGiftRewardManager.GetCurrentLevelRewards();
        var rewards2 = DiscountGiftRewardManager.GetCurrentLevelRewards();
        
        bool isSame = true;
        if (rewards1.Length != rewards2.Length)
        {
            isSame = false;
        }
        else
        {
            for (int i = 0; i < rewards1.Length; i++)
            {
                if (rewards1[i].itemId != rewards2[i].itemId || rewards1[i].count != rewards2[i].count)
                {
                    isSame = false;
                    break;
                }
            }
        }
        
        Debug.Log($"两次获取的奖励是否相同: {isSame}");
        
        if (isSame)
        {
            Debug.Log("✓ 测试通过：重复获取返回相同奖励");
        }
        else
        {
            Debug.LogError("✗ 测试失败：重复获取返回不同奖励");
        }
    }
    
    [ContextMenu("清理奖励数据")]
    public void TestCleanupRewards()
    {
        Debug.Log("=== 清理奖励数据 ===");
        DiscountGiftRewardManager.CleanupOldRewards();
        Debug.Log("清理完成");
    }
    
    [ContextMenu("清空所有存储数据")]
    public void ClearAllStorageData()
    {
        Debug.Log("=== 清空所有折扣礼品存储数据 ===");
        StorageMgr.DiscountGiftCurrentLevelRewards = "";
        StorageMgr.DiscountGiftLastShownLevel = 0;
        StorageMgr.DiscountGiftLastShownBattleType = 0;
        Debug.Log("清空完成");
    }
}
