fileFormatVersion: 2
guid: 3cedba3c7184a0a46998d903e0019403
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme2_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b41909c2f14e4224c857f76e51d31aa4
      internalID: -1652861703
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aaac0939683fa144cb98daac634e7638
      internalID: -863197826
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9f2a6e6286bc4d45af539953b10b1a2
      internalID: 375814955
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9718d32dd112c0944b6bc13f5b47bff5
      internalID: 766448969
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e231f7d4faaea548a4c0d0e00fca60c
      internalID: -782569466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c953d7485f7b97c46b3fdf5c5f371f29
      internalID: -1849633374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 190dc66ddd25c0d459b54e54731ed505
      internalID: -1491258074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76d2c043bc6c99042a34bf4d438f4955
      internalID: -59563042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8e3a91665470364ba17dcb54886bb3c
      internalID: 1811263478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f67f0cc7a19504540ade6cc64bc753ec
      internalID: -969726923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cef6a635fded4f440bd271b9cfc9b473
      internalID: -902983433
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9ed38771f4ed46041819c8863203d36c
      internalID: 1604778004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bc7627cfbf01c3646b5a1941fc19d0f5
      internalID: -2058007003
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6776d7a1a0611fd4fbff7903478e9203
      internalID: -940999189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 890f84de022e169448088d70aff2bd55
      internalID: 575767286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1fc12796b2bb0e4792e53960b3bb09f
      internalID: 185158129
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 129d557954b67884fa16172ea247b851
      internalID: 1553940241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: abb8aa7d0aaeb0c45a18441a798548d6
      internalID: -1170609452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e58d84b4793d714419c9c9e7bd504407
      internalID: 852495431
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c9c71fd0e4f3d9444992abee4ebe3faf
      internalID: -1792478670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d27ebc884ddede4c9aa97e5ff1eb780
      internalID: 478160634
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd2c58c420c92a147a253e3730878b5b
      internalID: 1366026686
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dfd878accc9b13f4cac3ac3fdae9246f
      internalID: -1985322316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb914af28e1ec2c418ca6951bd67a858
      internalID: 990545354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3378f7be0a9c024ba4f45d4bfd4377b
      internalID: 1533613702
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1e6cb10d2a75c6649bf61e1476a772e9
      internalID: -143597675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 60f6bf48ed6055f46985d3df10406e67
      internalID: -1187365873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 43cddb7088aa5834393f63e5a1c752ba
      internalID: -1949609863
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0ab9647b49795d40a8b289cf432a0ea
      internalID: 995931869
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ef3bfb00e2e9af24ab65eeac26014592
      internalID: 2024797192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: df313228e0c32354fa7c32bc6d835c40
      internalID: -2131315806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2adce6dc57a1e45498c8330c2e131a4f
      internalID: 1647327840
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ba26453066bba2f4e8f04ecba81d9011
      internalID: 916061813
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 292c5a436ed9489468f4b8e3e51033f5
      internalID: 2125996684
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b510b6ee5b08cea49b0628dc1194d35f
      internalID: -709081138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme2_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 32d374c611ab4284a8ee51bece20b5c7
      internalID: -834764248
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: ad088a4f5302d8547842f2d30fcbf71c
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme2_24: 1533613702
      theme2_16: 1553940241
      theme2_1: -863197826
      theme2_5: -1849633374
      theme2_33: 2125996684
      theme2_34: -709081138
      theme2_21: 1366026686
      theme2_12: -2058007003
      theme2_31: 1647327840
      theme2_15: 185158129
      theme2_27: -1949609863
      theme2_25: -143597675
      theme2_3: 766448969
      theme2_29: 2024797192
      theme2_30: -2131315806
      theme2_19: -1792478670
      theme2_28: 995931869
      theme2_20: 478160634
      theme2_2: 375814955
      theme2_0: -1652861703
      theme2_18: 852495431
      theme2_4: -782569466
      theme2_26: -1187365873
      theme2_9: -969726923
      theme2_8: 1811263478
      theme2_14: 575767286
      theme2_17: -1170609452
      theme2_6: -1491258074
      theme2_11: 1604778004
      theme2_23: 990545354
      theme2_22: -1985322316
      theme2_7: -59563042
      theme2_10: -902983433
      theme2_32: 916061813
      theme2_13: -940999189
      theme2_35: -834764248
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
