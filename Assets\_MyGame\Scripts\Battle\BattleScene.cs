using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Unity.VisualScripting;
using UnityEngine.Rendering.Universal;
using DG.Tweening;
using FairyGUI;
using Cysharp.Threading.Tasks;
using System.Threading.Tasks;
using Spine.Unity;

public enum GravityDirection
{
    None,
    Left,
    Right,
    Up,
    Down,
    Center2LR,
    LR2Center,
    Center2UD,
    UD2Center
}

public class BattleScene : MonoBehaviour
{
    public static BattleScene Inst { get; private set; }
    public Camera mainCamera;
    public LayerMask clickLayerMask;
    public Transform tileRoot;
    public SpriteRenderer bgRenderer;
    public GameObject gridPrefab;
    public GameObject tilePrefab;
    public GameObject tileIconPrefab;

    public LineRenderer pathLineRenderer; // 用于绘制路径的LineRenderer
    public Color successLineColor = Color.green;
    public Color failLineColor = Color.red;
    public Color selectedColor = Color.green;
    public Color promptColor = Color.cyan;


    public Action<BoardCell[,]> OnTileInit;
    public Action<BoardCell> OnTileSelected;
    public Action<bool> OnTileMatched;
    public Action<bool> OnGameDone;
    public Action<int, int> OnNoSolution;
    public Action<Vector3, int> OnCombo;
    public Action<Vector3> OnUseHammer;

    [Header("Tile Movement Settings")]
    public GravityDirection gravityDirection = GravityDirection.None;

    /// <summary>
    /// 最大拐点数量
    /// </summary>
    [NonSerialized] public int maxTurnPoint;

    /// <summary>
    /// 显示的最大拐点数量
    /// </summary>
    [NonSerialized] public int showMaxTurnPoint;
    /// <summary>
    /// 有生命限制
    /// </summary>
    [NonSerialized] public bool isLimitLife = false;

    /// <summary>
    /// 最大自动提示对数
    /// </summary>
    [NonSerialized] public int maxAutoPromptPairs = 3;
    /// <summary>
    /// 自动提示延迟
    /// </summary>
    private const float AUTO_PROMPT_DELAY = 5f;

    /// <summary>
    /// 特殊关卡自动提示延迟（第2、3、4关前40%进度）
    /// </summary>
    private const float SPECIAL_AUTO_PROMPT_DELAY = 7f;

    /// <summary>
    /// 是否已经检查过进度的标记（用于优化性能，避免重复计算进度）
    /// </summary>
    private bool hasCheckedProgress = false;

    /// <summary>
    /// 自动点击开关
    /// </summary>
    public bool isAutoClickEnabled = false;
    /// <summary>
    /// 自动点击延迟
    /// /// </summary>
    public float autoClickDelay = 0.5f;

    private int innerGridWidth = 8;
    private int innerGridHeight = 14;
    private int gridWidth;
    private int gridHeight;
    // private BoardGrid[,] grids;
    public Board board;
    private Tile3D selectedTile;
    private List<Vector2Int> pathPoints = new List<Vector2Int>();
    private List<CircleNum> activeCircleNums = new List<CircleNum>();
    private List<Vector3> pathLinePoints = new List<Vector3>();
    private TileManager tileManager;
    private TileAnimationManager tileAnimationManager;

    private float lastClickTime;
    private Tile3D lastClickTile;
    private const float DOUBLE_CLICK_TIME = 0.5f; // 双击判定时间，500毫秒

    private System.Threading.CancellationToken _destroyCancellationToken;

    private bool isReady = false;
    private bool isTileMoving = false;
    private float lastOperationTime;
    private int matchedPairsCount = 0;
    private bool hasClicked = false; // 记录是否已经点击过

    private TileMovementController movementController;

    private SkinSystem skinSystem;
    private ComboSystem comboSystem;

    private void Awake()
    {
        Inst = this;
        _destroyCancellationToken = this.GetCancellationTokenOnDestroy();
        if (Camera.main == mainCamera)
        {
            //直接从battle场景启动，测试
            mainCamera.AddComponent<AudioListener>();
            BattleResources.Inst.PreLoad(null).Forget();
            InitGrid(null, innerGridWidth, innerGridHeight, 2, 2, GravityDirection.None, null, null, 0, 0, false, new int[] { });
        }
        else
        {
            mainCamera.GetUniversalAdditionalCameraData().renderType = CameraRenderType.Overlay;
            var addCameraData = Camera.main.GetUniversalAdditionalCameraData();
            if (addCameraData.cameraStack != null && !addCameraData.cameraStack.Contains(mainCamera))
            {
                addCameraData.cameraStack.Insert(0, mainCamera);
            }
        }
    }
    public bool isInit;
    private PathFinder pathFinder;
    public void InitGrid(int[] types, InfoGate infoGate, int maxTurnPoint, int showMaxTurnPoint, int totalTileCount, Action OnGridCreated = null)
    {
        skinSystem = SystemFacade.SkinSystem;
        var isOnlyTopIcon = skinSystem.GetCurrentTheme().isOnlyTopIcon;
        InitGrid(types, infoGate.width, infoGate.height,
            maxTurnPoint, showMaxTurnPoint, infoGate.gravityDirection,
            infoGate.boardInfo, infoGate.typeCounts, totalTileCount,
            infoGate.matchingPairCount, isOnlyTopIcon, infoGate.specialPairs, OnGridCreated);
    }

    async public void InitGrid(int[] localTypes, int innerGridWidth, int innerGridHeight,
        int maxTurnPoint, int showMaxTurnPoint, GravityDirection gravityDirection,
        int[] boardInfo = null, int[] typeCounts = null, int totalTileCount = 0,
        int matchingPairCount = 0, bool isOnlyTopIcon = false, int[] specialPairs = null,
        Action OnGridCreated = null)
    {
        skinSystem = SystemFacade.SkinSystem;
        comboSystem = SystemFacade.ComboSystem;

        //延后一点提示
        lastOperationTime = Time.time + AUTO_PROMPT_DELAY;
        hasCheckedProgress = false;
        isAutoClickEnabled |= ConfigSetting.isAutoClickEnabled;

        this.innerGridWidth = innerGridWidth;
        this.innerGridHeight = innerGridHeight;

        gridWidth = innerGridWidth + 2; // 左右各加一列
        gridHeight = innerGridHeight + 2; // 上下各加一行

        this.gravityDirection = gravityDirection;
        this.maxTurnPoint = maxTurnPoint;
        this.showMaxTurnPoint = showMaxTurnPoint;

        // 初始化grid数组
        // grids = new BoardGrid[gridWidth, gridHeight];
        board = new Board(gridWidth, gridHeight);
        pathFinder = new PathFinder(board, showMaxTurnPoint);
        movementController = new TileMovementController(board, innerGridWidth, innerGridHeight);
        tileManager = new TileManager(pathFinder, maxTurnPoint, board, innerGridWidth, innerGridHeight);
        tileAnimationManager = new TileAnimationManager(board, innerGridWidth, innerGridHeight);

        int[] types = null;
        if (localTypes != null)
        {
            types = localTypes;
            maxAutoPromptPairs = 0;
        }
        else
        {
            var totalType = skinSystem.GetCurrentTheme().totalCount;
            types = tileManager.GenerateTileValues(totalType, boardInfo, typeCounts, matchingPairCount, specialPairs);
        }

        if (BattleResources.Inst.themeBg != null)
        {
            bgRenderer.sprite = BattleResources.Inst.themeBg;
        }
        // var skinTexture = BattleResources.Inst.themeTexture;

        // 启动分帧创建协程
        await CreateTilesAndGridsAsync(types, isOnlyTopIcon, null, totalTileCount);
        if (this == null)
            return;
        OnGridCreated?.Invoke();
        // 显示瓦片动画
        ShowTilesSequentially().Forget();

        isInit = true;
    }

    /// <summary>
    /// 分帧创建Tiles和Grids
    /// </summary>
    private async UniTask CreateTilesAndGridsAsync(int[] types, bool isOnlyTopIcon, Texture skinTexture, int totalTileCount)
    {
        // 每帧创建的对象数量
        const int OBJECTS_PER_FRAME = 20;
        int objectsCreatedThisFrame = 0;

        // 分帧创建Tile对象
        for (int y = 1; y <= innerGridHeight; y++)
        {
            for (int x = 1; x <= innerGridWidth; x++)
            {
                int index = (x - 1) + (y - 1) * innerGridWidth;
                var block = new BoardCell(types[index], new Vector2Int(x, y));
                board.SetBlock(block);

                CreateTile(isOnlyTopIcon, block, skinTexture);

                objectsCreatedThisFrame++;
                if (objectsCreatedThisFrame >= OBJECTS_PER_FRAME)
                {
                    objectsCreatedThisFrame = 0;
                    await UniTask.Yield(); // 等待下一帧
                }
            }
        }

        // 分帧创建Grid对象
        // objectsCreatedThisFrame = 0;
        // for (int x = 0; x < gridWidth; x++)
        // {
        //     for (int y = 0; y < gridHeight; y++)
        //     {
        //         Vector3 worldPos = board.GridToWorldPosition(new Vector2Int(x, y));
        //         var grid = Instantiate(gridPrefab, worldPos, Quaternion.identity, tileRoot).GetComponent<BoardGrid>();
        //         grid.gameObject.SetActive(false);
        //         grids[x, y] = grid;

        //         objectsCreatedThisFrame++;
        //         if (objectsCreatedThisFrame >= OBJECTS_PER_FRAME)
        //         {
        //             objectsCreatedThisFrame = 0;
        //             await UniTask.Yield(); // 等待下一帧
        //         }
        //     }
        // }

        // 初始化瓦片管理器的缓存
        tileManager.InitializeCache(totalTileCount);
    }

    public Tile3D CreateTile(bool isOnlyTopIcon, BoardCell cell, Texture skinTexture)
    {
        if (cell.type == 0)
        {
            return null;
        }

        var worldPos = board.GridToWorldPosition(cell.pos);
        var tile = GameObject.Instantiate(BattleScene.Inst.tilePrefab, worldPos, Quaternion.identity, BattleScene.Inst.tileRoot).GetComponent<Tile3D>();
        tile.freezeLayer = 0;
        tile.SetData(cell, this);
        cell.SetTile(tile);
        return tile;
    }

    private async UniTaskVoid ShowTilesSequentially()
    {
        SoundManager.PlayEffect(SoundPath.ui_paveTile);
        switch (GameGlobal.EnterLevel)
        {
            case 1:
                await tileAnimationManager.DiagonalIn();
                break;
            case 2:
                await tileAnimationManager.RollInFromRight();
                break;
            case 3:
                await tileAnimationManager.WaveDrop();
                break;
            case 4:
                await tileAnimationManager.ExpandFromCenter();
                break;
            default:
                await tileAnimationManager.RandomIn();
                break;
        }

        OnTileInit?.Invoke(board.grid);
        isReady = true;


        var hasMatchPair = CheckSolution();
        if (hasMatchPair)
        {
            StartAutoClick().Forget();
        }
    }

    async private UniTaskVoid StartAutoClick()
    {
        if (!isAutoClickEnabled)
            return;
        await UniTask.Delay(2000);
        AutoClickMatch();
    }
    async private UniTaskVoid NextAutoClick()
    {
        if (!isAutoClickEnabled)
            return;
        await UniTask.Delay(Mathf.FloorToInt(autoClickDelay * 1000));
        AutoClickMatch();
    }

    private void Update()
    {
        if (!isReady)
            return;

        if (Input.GetMouseButtonDown(0) && !hasClicked)
        {
            hasClicked = true;
            if (IsTouchUI)
                return;

            var ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, 100, clickLayerMask))
            {
                var tile = hit.collider.gameObject.GetComponentInParent<Tile3D>();
                if (tile != null)
                {
                    Platform.Instance.VibrateShort();
                    OnTileClick(tile);
                }
            }
        }

        if (NeedAutoPromptMatch())
        {
            AutoPromptMatch();
            lastOperationTime = Time.time;
        }

        if (Input.GetMouseButtonUp(0))
        {
            hasClicked = false;
        }
    }


    private void UpdateLastOperationTime()
    {
        lastOperationTime = Time.time;
    }

    /// <summary>
    /// 获取上次操作时间
    /// </summary>
    /// <returns>上次操作的时间戳</returns>
    public float GetLastOperationTime()
    {
        return lastOperationTime;
    }

    /// <summary>
    /// 获取距离上次操作的时间间隔
    /// </summary>
    /// <returns>时间间隔（秒）</returns>
    public float GetTimeSinceLastOperation()
    {
        return Time.time - lastOperationTime;
    }

    private bool NeedAutoPromptMatch()
    {
        // 检查是否是第2、3、4关
        int level = GameGlobal.EnterLevel;
        if (level >= 2 && level <= 4)
        {
            if (hasCheckedProgress || Time.time - lastOperationTime < SPECIAL_AUTO_PROMPT_DELAY)
                return false;

            // 计算当前关卡进度百分比
            int totalTileCount = GetTotalTileCount();
            int remainingTileCount = GetRemainingTileCount();
            float progress = (totalTileCount - remainingTileCount) / (float)totalTileCount;

            // 进度小于40%才触发
            if (progress < 0.4f)
            {
                return true;
            }
            else
            {
                //后续不再检测
                hasCheckedProgress = true;
            }
        }
        else
        {
            //次数满足就触发
            if (matchedPairsCount < maxAutoPromptPairs && Time.time - lastOperationTime > AUTO_PROMPT_DELAY)
            {
                return true;
            }
        }
        return false;
    }

    private static string defaultAniName = "animation";
    private void PlayTileEffect(Tile3D tile, bool isCrush = false, bool playSound = false)
    {
        var poolName = string.Empty;
        var soundName = string.Empty;
        if (tile.IsFreeze)
        {
            poolName = isCrush ? PoolNames.Pool_EffectIceCrush : PoolNames.Pool_EffectIceClick;
            soundName = isCrush ? SoundPath.sfx_crushIce : SoundPath.sfx_clickIce;
        }
        else if (tile.IsChain)
        {
            poolName = isCrush ? PoolNames.Pool_EffectVineCrush : PoolNames.Pool_EffectVineClick;
            soundName = isCrush ? SoundPath.sfx_crushVine : SoundPath.sfx_clickVine;
        }
        else if (tile.IsWood)
        {
            poolName = PoolNames.Pool_EffectWoodClick;
            soundName = SoundPath.sfx_clickWood;
        }
        else if (tile.IsStone)
        {
            poolName = PoolNames.Pool_EffectStoneClick;
            soundName = SoundPath.sfx_clickStone;
        }
        else if (tile.IsQuestion)
        {
            soundName = isCrush ? SoundPath.sfx_crushFlip : SoundPath.sfx_clickFlip;
        }

        if (playSound && !string.IsNullOrEmpty(soundName))
        {
            SoundManager.PlayEffect(soundName);
        }

        if (string.IsNullOrEmpty(poolName))
            return;

        var effect = BattleResources.Inst.GetPoolItem(poolName);
        if (effect != null)
        {
            var ani = effect.GetComponentInChildren<SkeletonAnimation>();
            if (ani != null)
            {
                ani.Play(defaultAniName);
            }
            effect.transform.position = tile.transform.position;
            Timers.inst.Add(1, 1, (obj) =>
            {
                if (this == null) return;
                effect.Release();
            });
        }
    }

    private void OnTileClick(Tile3D tile)
    {
        if (GetTileAt(tile.Block.pos) == null)//已经消除了
            return;

        UpdateLastOperationTime();
        tile.OnClick?.Invoke();
        OnTileSelected?.Invoke(tile.Block);

        if (!tile.CanSelect)
        {
            PlayTileEffect(tile, false, true);
            SoundManager.PlayEffect("chip_error");
            return;
        }

        // 双击同一个tile时，让所有相同type的tile都执行OnDoubleClick
        float timeSinceLastClick = Time.time - lastClickTime;
        if (lastClickTile == tile && timeSinceLastClick <= DOUBLE_CLICK_TIME)
        {
            var tileType = tile.Block.type;
            for (int x = 1; x < gridWidth; x++)
            {
                for (int y = 1; y < gridHeight; y++)
                {
                    var cell = board.grid[x, y];
                    if (cell != null && !cell.IsEmpty && cell.type == tileType)
                    {
                        cell.tile.OnDoubleClick();
                    }
                }
            }
            lastClickTile = null;
        }
        else
        {
            lastClickTile = tile;
            lastClickTime = Time.time;
        }

        if (selectedTile == null)
        {
            SelectTile(tile);
            SoundManager.PlayEffect("chip_click");
        }
        else if (selectedTile == tile)
        {
            selectedTile.OnDeselect();
            selectedTile = null;
            SoundManager.PlayEffect("chip_click");
        }
        else
        {
            if (selectedTile.Block.type == tile.Block.type)
            {
                tile.OnSelect();
                var pathPoints = CheckConnection(selectedTile, tile);
                if (pathPoints != null)
                {
                    // var turnCount = GetTurnPointCount(pathPoints);
                    // if (turnCount <= maxTurnPoint)
                    // {
                    matchedPairsCount++;
                    MatchTiles(selectedTile, tile);
                    MatchTilesAnimation(pathPoints, selectedTile, tile).Forget();

                    UnpromptMatchGroup();

                    selectedTile = null;
                    // }
                    // else
                    // {
                    //     HandleUnmatchTilesAsync(pathPoints, selectedTile, tile).Forget();
                    //     TipMgr.ShowTip(LangUtil.GetText("txtTurningPointsOut", maxTurnPoint), 1);//"拐点超过{0}个"
                    //     OnTileMatched?.Invoke(false);
                    // }
                }
                else
                {
                    HandleUnmatchTilesAsync(pathPoints, selectedTile, tile).Forget();
                    TipMgr.ShowTip(LangUtil.GetText("txtTurningPointsOut", maxTurnPoint));//"拐点超过{0}个"
                    OnTileMatched?.Invoke(false);
                }
            }
            else
            {
                if (isLimitLife)
                {
                    if (selectedTile != null)
                    {
                        selectedTile.OnDeselect();
                        selectedTile = null;
                    }
                    SoundManager.PlayEffect("chip_error");
                }
                else
                {
                    //没生命限制的情况
                    tile.OnSelect();
                    SelectTile(tile);
                    SoundManager.PlayEffect("chip_click");
                }
                OnTileMatched?.Invoke(false);
            }
        }
    }


    Vector2Int[] directions = new Vector2Int[]
            {
                            new Vector2Int(0, 1),   // 上
                            new Vector2Int(0, -1),  // 下
                            new Vector2Int(-1, 0),  // 左
                            new Vector2Int(1, 0)    // 右
            };
    private void MatchTiles(Tile3D tileA, Tile3D tileB)
    {
        // 移除瓦片
        tileManager.RemoveMatchedPair(tileA.Block.pos, tileB.Block.pos);

        bool hasOpenTile = false;
        // 检查第一个消除位置周围
        foreach (var dir in directions)
        {
            Vector2Int checkPos = new Vector2Int(tileA.Block.pos.x, tileA.Block.pos.y) + dir;
            var nearTile = GetTileAt(checkPos);
            if (nearTile != null)
            {
                if (nearTile.IsChain || nearTile.IsFreeze || nearTile.IsQuestion)
                {
                    PlayTileEffect(nearTile, true, true);
                }
                hasOpenTile |= nearTile.TryOpenTile();
            }
        }

        // 检查第二个消除位置周围
        foreach (var dir in directions)
        {
            Vector2Int checkPos = new Vector2Int(tileB.Block.pos.x, tileB.Block.pos.y) + dir;
            var nearTile = GetTileAt(checkPos);
            if (nearTile != null)
            {
                if (nearTile.IsChain || nearTile.IsFreeze || nearTile.IsQuestion)
                {
                    PlayTileEffect(nearTile, true, true);
                }
                hasOpenTile |= nearTile.TryOpenTile();
            }
        }

        if (hasOpenTile)
        {
            tileManager.RefreshTypeCache();
        }
    }

    #region 爆竹
    private async UniTask LaunchFirecrackers(Vector3 startPos, List<(Tile3D, Tile3D)> firecrackerTargetTiles)
    {
        var targetPairs = firecrackerTargetTiles;
        // 如果没有找到任何对子，直接返回
        if (targetPairs == null || targetPairs.Count == 0)
            return;

        var moveTime = 1f;
        var delayTime = 0.1f;
        // 为每对砖块生成2个爆竹
        for (int i = 0; i < targetPairs.Count; i++)
        {
            var pair = targetPairs[i];

            // 为每对砖块的两个砖块各生成一个爆竹
            LaunchFirecracker(startPos, pair.Item1, moveTime).Forget();
            await UniTask.Delay(TimeSpan.FromSeconds(delayTime), cancellationToken: _destroyCancellationToken);
            LaunchFirecracker(startPos, pair.Item2, moveTime).Forget();
            await UniTask.Delay(TimeSpan.FromSeconds(delayTime), cancellationToken: _destroyCancellationToken);
        }
        await UniTask.Delay(TimeSpan.FromSeconds(moveTime), cancellationToken: _destroyCancellationToken);
    }

    private async UniTaskVoid LaunchFirecracker(Vector3 startPos, Tile3D tile, float moveTime)
    {
        // 从对象池获取爆竹
        var firecrackerItem = BattleResources.Inst.CreateFirecracker();
        if (firecrackerItem == null)
            return;

        var firecracker = firecrackerItem.gameObject;

        // 设置初始位置
        firecracker.transform.position = startPos;
        firecracker.transform.SetParent(tileRoot);

        // 初始朝向 - 朝上
        Vector3 initialDirection = Vector3.up;
        firecracker.transform.rotation = Quaternion.LookRotation(initialDirection);

        // 计算飞行路径的控制点（形成大弧线形状）
        float pathHeight = 5.0f; // 路径高度
        float pathWidth = 2.0f;  // 路径宽度

        // 计算起点到目标点的方向向量
        var targetPos = tile.transform.position;
        Vector3 direction = (targetPos - startPos).normalized;
        Vector3 right = Vector3.Cross(Vector3.up, direction).normalized; // 右向量

        // 创建弧线路径点
        Vector3[] pathPoints = new Vector3[5];
        pathPoints[0] = startPos; // 起点

        // 计算中间控制点 - 形成一个大弧线
        // 第一个控制点 - 向上抬升
        pathPoints[1] = startPos + new Vector3(UnityEngine.Random.Range(-1f, 1f), pathHeight * 0.5f, UnityEngine.Random.Range(-2f, 2f)); // Vector3.up * pathHeight * 0.5f;

        // 第二个控制点 - 弧线最高点
        pathPoints[2] = Vector3.Lerp(startPos, targetPos, 0.5f) + Vector3.up * pathHeight;

        // 第三个控制点 - 向右偏移形成"P"形状的顶视图
        Vector3 midPoint = Vector3.Lerp(startPos, targetPos, 0.7f);
        pathPoints[3] = new Vector3(
            midPoint.x + right.x * pathWidth,
            midPoint.y + pathHeight * 0.7f,
            midPoint.z + right.z * pathWidth
        );

        // 目标点
        pathPoints[4] = targetPos;

        // 创建路径动画
        var sequence = DOTween.Sequence();

        sequence.Append(
            firecracker.transform.DOPath(
                pathPoints,
                moveTime, // 总时间
                PathType.CatmullRom, // 使用CatmullRom路径类型获得平滑曲线
                PathMode.Full3D,
                10, // 路径分段数
                Color.red // 调试用路径颜色
            )
            .SetEase(Ease.Linear)
            .SetLookAt(0.01f) // 设置朝向跟随路径，参数是提前量
        );

        SoundManager.PlayEffect("firecracker_fly");
        // 到达目标后播放爆炸效果并回收爆竹
        sequence.OnComplete(() =>
        {
            // 播放爆炸效果
            PlayFirecrackerExplosion(targetPos).Forget();
            SoundManager.PlayEffect("firecracker_boom");

            // 回收爆竹到对象池
            firecrackerItem.Release();
            tile.OnRemove();
        });

        await UniTask.CompletedTask;
    }

    private async UniTaskVoid PlayFirecrackerExplosion(Vector3 pos)
    {
        if (this == null)
            return;

        pos.y += 0.5f;
        var explosion = BattleResources.Inst.CreateFirecrackerExplosion();
        explosion.transform.position = pos;
        await UniTask.Delay(TimeSpan.FromSeconds(0.6f), cancellationToken: _destroyCancellationToken);
        explosion.Release();

    }
    #endregion

    /// <summary>
    /// 检查是否有解
    /// </summary>
    /// <returns>true:有解</returns>
    private bool CheckSolution(bool showTip = true)
    {
        var hasMatch = tileManager.HasValidMatch();
        if (hasMatch)
            return true;

        if (showTip)
        {
            var curCount = tileManager.GetRemainingTileCount();
            var totalCount = tileManager.GetTotalTileCount();
            OnNoSolution?.Invoke(curCount, totalCount);
        }
        return false;
    }

    public int GetRemainingTileCount()
    {
        return tileManager.GetRemainingTileCount();
    }
    public int GetTotalTileCount()
    {
        return tileManager.GetTotalTileCount();
    }

    #region 消除表现
    private float basePitch = 1f;  // 基础音调
    private float pitchIncrement = 0.08f;  // 音调增量
    private async UniTaskVoid MatchTilesAnimation(List<Vector2Int> pathPoints, Tile3D tileA, Tile3D tileB)
    {
        tileA.OnDeselect();
        tileB.OnDeselect();
        comboSystem.OnMatch();

        // 将路径点转换为世界坐标
        var worldPathPoints = pathPoints.Select(board.GridToWorldPosition).ToList();
        Vector3 midPathWorldPos;
        if (worldPathPoints.Count % 2 == 0)
        {
            // 偶数路径点
            int midIndex = worldPathPoints.Count / 2;
            midPathWorldPos = (worldPathPoints[midIndex - 1] + worldPathPoints[midIndex]) / 2f;
        }
        else
        {
            // 奇数路径点
            int midIndex = worldPathPoints.Count / 2;
            midPathWorldPos = worldPathPoints[midIndex];
        }

        PlayComboEffect(midPathWorldPos);

        if (this == null)
            return;

        SoundManager.PlayEffect("chip_match");
        RemoveTiles(tileA, tileB);

        List<(Tile3D, Tile3D)> firecrackerTargetTiles = null;
        if (tileA.IsFirecracker)
        {
            if (gravityDirection != GravityDirection.None)
            {
                ShiftTilesCoroutine().Forget();
            }

            var firecrackerTargetPairs = tileManager.FindRandomPairs(ConfigSetting.firecrackerMatchCount, true);
            firecrackerTargetTiles = new List<(Tile3D, Tile3D)>();
            foreach (var pair in firecrackerTargetPairs)
            {
                var targetTileA = GetTileAt(pair.Item1);
                var targetTileB = GetTileAt(pair.Item2);
                if (targetTileA != null && targetTileB != null)
                {
                    firecrackerTargetTiles.Add((targetTileA, targetTileB));
                }
                tileManager.RemoveTile(pair.Item1);
                tileManager.RemoveTile(pair.Item2);
            }
        }
        UniTask firecrackerTask = UniTask.CompletedTask;
        if (firecrackerTargetTiles != null)
        {
            firecrackerTask = LaunchFirecrackers(midPathWorldPos, firecrackerTargetTiles);
        }

        // 播放匹配特效
        var duration = 0.4f;
        PlayMatchEffects(worldPathPoints, tileA, tileB, duration);
        await UniTask.Delay(TimeSpan.FromSeconds(duration), cancellationToken: _destroyCancellationToken);

        if (firecrackerTargetTiles != null)
        {
            await firecrackerTask;
            if (this == null)
                return;
        }

        OnMatchComplete();
    }

    private void PlayComboEffect(Vector3 scenePos)
    {
        var comboCount = comboSystem.GetCurrentCombo();
        if (comboCount > 0)
        {
            OnCombo?.Invoke(scenePos, comboCount);
        }
    }

    #region  闪电效果
    private void PlayMatchEffects(List<Vector3> worldPathPoints, Tile3D tileA, Tile3D tileB, float duration)
    {
        // 在消除的方块上播放EffectFlashPoint特效
        PlayFlashPointEffect(tileA.transform.position, duration);
        PlayFlashPointEffect(tileB.transform.position, duration);

        // 在闪电拐角处播放EffectFlashTurn特效
        var turnPoints = GetTurnPoints(worldPathPoints);
        foreach (var turnPoint in turnPoints)
        {
            PlayFlashTurnEffect(turnPoint, duration);
        }

        // 在路径上播放闪电特效
        PlayLightningEffects(worldPathPoints, duration);
    }

    private void PlayFlashPointEffect(Vector3 position, float duration)
    {
        var effect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectFlashPoint);
        if (effect != null)
        {
            effect.transform.position = position;
            effect.transform.rotation = Quaternion.identity;
            effect.transform.localScale = Vector3.one;

            Timers.inst.Add(duration, 1, (obj) =>
            {
                if (this == null) return;
                effect.Release();
            });
        }
    }

    private void PlayFlashTurnEffect(Vector3 position, float duration)
    {
        var effect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectFlashTurn);
        if (effect != null)
        {
            effect.transform.position = position;
            effect.transform.rotation = Quaternion.identity;
            effect.transform.localScale = Vector3.one;

            Timers.inst.Add(duration, 1, (obj) =>
            {
                if (this == null) return;
                effect.Release();
            });
        }
    }

    private void PlayLightningEffects(List<Vector3> worldPathPoints, float duration)
    {
        if (worldPathPoints.Count < 2) return;

        // 根据路径点创建闪电段
        var segments = CreateLightningSegments(worldPathPoints);

        for (int i = 0; i < segments.Count; i++)
        {
            var segment = segments[i];
            CreateLightningForSegment(segment, duration);
        }
    }

    private void CreateLightningForSegment(LightningSegment segment, float duration)
    {
        float tileSize = 0.6f;//0.6f是方块的尺寸
        float segmentLength = Vector3.Distance(segment.start, segment.end) / tileSize;
        float defaultLightningLength = 3f;
        if (segmentLength <= defaultLightningLength)
        {
            // 长度小于等于3的用1个闪电缩放
            CreateSingleLightning(segment.start, segment.end, segmentLength / defaultLightningLength, duration);
        }
        else
        {
            // 长度大于3的用多个闪电拼接
            int lightningCount = Mathf.CeilToInt(segmentLength / defaultLightningLength);
            Log.Info($"lightningCount:{lightningCount}");
            Vector3 direction = (segment.end - segment.start).normalized;

            for (int i = 0; i < lightningCount; i++)
            {
                Vector3 startPos = segment.start + direction * (i * defaultLightningLength * tileSize);
                Vector3 endPos;
                float scale = 1f;

                if (i == lightningCount - 1)
                {
                    // 最后一个闪电，计算剩余长度并缩放
                    endPos = segment.end;
                    float remainingLength = Vector3.Distance(startPos, endPos) / tileSize;
                    scale = remainingLength / defaultLightningLength;
                }
                else
                {
                    // 中间的闪电，使用默认长度
                    endPos = startPos + direction * defaultLightningLength * tileSize;
                }

                CreateSingleLightning(startPos, endPos, scale, duration);
            }
        }
    }

    private void CreateSingleLightning(Vector3 startPos, Vector3 endPos, float scale, float duration)
    {
        var effect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectFlash);
        if (effect != null)
        {
            // 设置位置为线段中点
            Vector3 midPoint = (startPos + endPos) / 2f;
            effect.transform.position = midPoint;

            // 计算旋转：闪电特效在xz平面并指向z，需要旋转到线段方向
            Vector3 direction = (endPos - startPos).normalized;
            if (direction != Vector3.zero)
            {
                // 计算在xz平面上的旋转
                Vector3 xzDirection = new Vector3(direction.x, 0, direction.z).normalized;
                if (xzDirection != Vector3.zero)
                {
                    effect.transform.rotation = Quaternion.LookRotation(xzDirection, Vector3.up);
                }
            }
            // 设置缩放
            effect.transform.localScale = new Vector3(1f, 1f, Mathf.Max(0.1f, scale));

            // 延时回收（稍微延长一点时间确保动画播放完整）
            Timers.inst.Add(duration, 1, (obj) =>
            {
                if (this == null) return;
                effect.Release();
            });
        }
    }

    private struct LightningSegment
    {
        public Vector3 start;
        public Vector3 end;

        public LightningSegment(Vector3 start, Vector3 end)
        {
            this.start = start;
            this.end = end;
        }
    }

    private List<LightningSegment> CreateLightningSegments(List<Vector3> pathPoints)
    {
        var segments = new List<LightningSegment>();

        if (pathPoints.Count < 2) return segments;

        // 根据路径的转折点数量决定闪电段数
        var turnPoints = GetTurnPoints(pathPoints);
        var startPoint = pathPoints[0];
        var endPoint = pathPoints[^1];

        if (turnPoints.Count == 0)
        {
            // 直线路径：1条闪电
            segments.Add(new LightningSegment(startPoint, endPoint));
        }
        else if (turnPoints.Count == 1)
        {
            // 1个转折点：2条闪电
            segments.Add(new LightningSegment(startPoint, turnPoints[0]));
            segments.Add(new LightningSegment(turnPoints[0], endPoint));
        }
        else
        {
            // 2个或更多转折点：3条闪电（取前两个转折点）
            segments.Add(new LightningSegment(startPoint, turnPoints[0]));
            segments.Add(new LightningSegment(turnPoints[0], turnPoints[1]));
            segments.Add(new LightningSegment(turnPoints[1], endPoint));
        }

        return segments;
    }

    private List<Vector3> GetTurnPoints(List<Vector3> pathPoints)
    {
        var turnPoints = new List<Vector3>();

        if (pathPoints.Count <= 2) return turnPoints;

        for (int i = 1; i < pathPoints.Count - 1; i++)
        {
            Vector3 prevDirection = (pathPoints[i] - pathPoints[i - 1]).normalized;
            Vector3 nextDirection = (pathPoints[i + 1] - pathPoints[i]).normalized;

            // 如果方向变化超过一定角度，认为是转折点
            if (Vector3.Dot(prevDirection, nextDirection) < 0.99f)
            {
                turnPoints.Add(pathPoints[i]);
            }
        }

        return turnPoints;
    }
    #endregion

    private async UniTaskVoid PlayExplosion(Vector3 pos)
    {
        if (this == null)
            return;

        pos.y += 0.5f;
        var explosion = BattleResources.Inst.CreateExplosion();
        explosion.transform.position = pos;
        await UniTask.Delay(TimeSpan.FromSeconds(0.6f), cancellationToken: _destroyCancellationToken);
        explosion.Release();
    }

    private void OnMatchComplete()
    {
        if (gravityDirection != GravityDirection.None)
        {
            ShiftTilesCoroutine().Forget();
        }
        OnTileMatched?.Invoke(true);
        CheckWin();
    }

    private void CheckWin()
    {
        bool isWin = !tileManager.HasRemainingTiles();
        if (isWin)
        {
            OnGameDone?.Invoke(true);
        }
        else
        {
            var hasMatchPair = CheckSolution();
            if (hasMatchPair)
            {
                NextAutoClick().Forget();
            }
        }
    }
    #endregion

    private async UniTaskVoid HandleUnmatchTilesAsync(List<Vector2Int> pathPoints, Tile3D tileA, Tile3D tileB)
    {
        if (pathPoints != null)
        {
            CreateTurnPath(pathPoints);
        }

        await UniTask.Delay(TimeSpan.FromSeconds(0.1f), cancellationToken: _destroyCancellationToken);
        tileA.OnUnmatch();
        tileB.OnUnmatch();
        SoundManager.PlayEffect("chip_error");

        await UniTask.Delay(TimeSpan.FromSeconds(0.3f), cancellationToken: _destroyCancellationToken);
        if (tileA.type == tileB.type)
        {
            tileA.OnDeselect();
            tileB.OnDeselect();
            selectedTile = null;
        }

        if (isLimitLife)
        {
            if (selectedTile != null)
            {
                selectedTile.OnDeselect();
                selectedTile = null;
            }
        }
        await UniTask.Delay(TimeSpan.FromSeconds(0.2f), cancellationToken: _destroyCancellationToken);
        ClearCircleNums();
    }

    private void SelectTile(Tile3D tile)
    {
        if (selectedTile != null)
        {
            selectedTile.OnDeselect();
        }
        selectedTile = tile;
        tile.OnSelect();
    }

    private int GetTurnPointCount(List<Vector2Int> pathPoints)
    {
        if (pathPoints == null || pathPoints.Count <= 2)
        {
            return 0;
        }

        int turnPointCount = 0;
        for (int i = 1; i < pathPoints.Count - 1; i++)
        {
            // 计算前一个线段和后一个线段的方向
            Vector2Int prevDirection = pathPoints[i] - pathPoints[i - 1];
            Vector2Int nextDirection = pathPoints[i + 1] - pathPoints[i];

            // 如果方向不同，则是转折点
            if (prevDirection != nextDirection)
            {
                turnPointCount++;
            }
        }

        return turnPointCount;
    }

    private List<Vector2Int> CheckConnection(Tile3D tileA, Tile3D tileB)
    {
        pathPoints = pathFinder.FindPath(tileA.Block.pos, tileB.Block.pos);
        return pathPoints;
    }

    private int CreateTurnPath(List<Vector2Int> pathPoints)
    {
        ClearCircleNums();

        pathLinePoints.Clear();
        int turnPointIndex = 0; // 拐点计数器
        for (int i = 0; i < pathPoints.Count; i++)
        {
            Vector3 position = board.GridToWorldPosition(pathPoints[i]);

            // 添加路径点
            pathLinePoints.Add(position);

            // 只在拐点添加CircleNum
            if (i > 0 && i < pathPoints.Count - 1)
            {
                // 判断是否为拐点（方向发生变化）
                Vector2Int prevDirection = pathPoints[i] - pathPoints[i - 1];
                Vector2Int nextDirection = pathPoints[i + 1] - pathPoints[i];

                if (prevDirection != nextDirection)
                {
                    var circleNum = BattleResources.Inst.CreateCircleNum();
                    circleNum.transform.position = position;
                    circleNum.SetData(turnPointIndex); // 使用拐点计数器作为索引
                    activeCircleNums.Add(circleNum);

                    turnPointIndex++; // 拐点计数器递增
                }
            }
        }

        var isSuccess = turnPointIndex <= maxTurnPoint;
        var showDuration = isSuccess ? 0.3f : 0.5f;
        // 绘制路径线
        if (pathLineRenderer != null)
        {
            pathLineRenderer.startColor = pathLineRenderer.endColor = isSuccess ? successLineColor : failLineColor;
            pathLineRenderer.positionCount = pathLinePoints.Count;
            pathLineRenderer.SetPositions(pathLinePoints.ToArray());
        }
        return turnPointIndex;
    }

    private void RemoveTiles(Tile3D tileA, Tile3D tileB)
    {
        tileA.OnRemove();
        tileB.OnRemove();
    }

    private void ClearCircleNums()
    {
        foreach (var circleNum in activeCircleNums)
        {
            circleNum.Release();
        }
        activeCircleNums.Clear();
        ClearPathLine();
    }

    private void ClearPathLine()
    {
        if (pathLineRenderer != null)
        {
            pathLineRenderer.positionCount = 0;
        }
        pathLinePoints.Clear();
    }

    private bool IsTouchUI
    {
        get
        {
            if (Camera.main == mainCamera)
                return false;
            return GRoot.inst.touchTarget != null;
        }
    }

    private void OnDestroy()
    {
        OnTileInit = null;
        OnTileSelected = null;
        OnTileMatched = null;
        OnGameDone = null;
        OnNoSolution = null;
        OnCombo = null;
        OnUseHammer = null;

        // 清理所有Tile对象
        CleanupAllTiles();

        if (Camera.main != null)
        {
            var cameraStack = Camera.main.GetUniversalAdditionalCameraData().cameraStack;
            if (cameraStack != null && cameraStack.Contains(mainCamera))
            {
                cameraStack.Remove(mainCamera);
            }
        }
    }

    /// <summary>
    /// 清理所有Tile对象
    /// </summary>
    private void CleanupAllTiles()
    {
        if (board == null || board.grid == null)
            return;

        // 销毁所有Tile对象
        for (int x = 0; x < gridWidth; x++)
        {
            for (int y = 0; y < gridHeight; y++)
            {
                if (board.grid[x, y] != null && board.grid[x, y].tile != null)
                {
                    Destroy(board.grid[x, y].tile.gameObject);
                    board.grid[x, y].SetTile(null);
                }
            }
        }
        Tile3D.ClearMaterialCache();

        // 销毁所有Grid对象
        // if (grids != null)
        // {
        //     for (int x = 0; x < gridWidth; x++)
        //     {
        //         for (int y = 0; y < gridHeight; y++)
        //         {
        //             if (grids[x, y] != null)
        //             {
        //                 Destroy(grids[x, y].gameObject);
        //             }
        //         }
        //     }
        // }

        // 清理其他可能的引用
        selectedTile = null;
        promptedTileA = null;
        promptedTileB = null;
        activeCircleNums?.Clear();
        pathPoints?.Clear();
        pathLinePoints?.Clear();
    }

    // 根据移动方向确定旋转轴
    private Vector3 DetermineRotationAxis(Vector3 moveDirection)
    {
        // 绝对值最大的轴作为旋转轴
        if (Mathf.Abs(moveDirection.x) >= Mathf.Abs(moveDirection.z))
        {
            // X轴移动，绕Z轴旋转
            return moveDirection.x > 0 ? -Vector3.forward : Vector3.forward;
        }
        else
        {
            // Z轴移动，绕X轴旋转
            return moveDirection.z > 0 ? Vector3.right : -Vector3.right;
        }

        // // 判断应该在哪个轴上滚动（选择差值较大的轴）
        // bool moveOnXAxis = Mathf.Abs(moveDirection.x) > 0;

        // // 确定移动方向
        // float targetX = moveOnXAxis ? (moveDirection.x > 0 ? 1 : -1) : 0;
        // float targetZ = !moveOnXAxis ? (moveDirection.z > 0 ? 1 : -1) : 0;

        // Vector3 rotationAxis = moveOnXAxis ? Vector3.forward : Vector3.right; // 如果在X轴移动，绕Z轴旋转；如果在Z轴移动，绕X轴旋转
        // float rotationDirection = moveOnXAxis ? -targetX : targetZ; // 根据移动方向确定旋转方向

        // return rotationAxis * rotationDirection;
    }

    public int GetNeighborPairCount()
    {
        return tileManager.GetNeighborPairCount();
    }

    #region 使用锤子
    public bool CleanUpMatchGroup()
    {
        var matchablePairs = tileManager.FindRandomPairs(ConfigSetting.hammerMatchCount, true);

        if (matchablePairs == null || matchablePairs.Count == 0)
            return false;

        // 验证所有瓦片都存在
        var validPairs = new List<(Tile3D, Tile3D)>();
        foreach (var pair in matchablePairs)
        {
            var tileA = GetTileAt(pair.Item1);
            var tileB = GetTileAt(pair.Item2);
            if (tileA != null && tileB != null)
            {
                validPairs.Add((tileA, tileB));
            }
        }

        if (validPairs.Count == 0)
            return false;

        OnCleanUpMatchGroup(validPairs).Forget();
        UnpromptMatchGroup();
        return true;
    }

    async private UniTaskVoid OnCleanUpMatchGroup(List<(Tile3D, Tile3D)> tilePairs)
    {
        // 先更新数据
        foreach (var pair in tilePairs)
        {
            var tileA = pair.Item1;
            var tileB = pair.Item2;
            MatchTiles(tileA, tileB);
        }

        foreach (var pair in tilePairs)
        {
            var tileA = pair.Item1;
            var tileB = pair.Item2;

            OnUseHammer?.Invoke(tileA.transform.position);
            await UniTask.Delay(TimeSpan.FromSeconds(0.15f), cancellationToken: _destroyCancellationToken);
            OnUseHammer?.Invoke(tileB.transform.position);

            // PlayExplosion(tileA.transform.position).Forget();
            // PlayExplosion(tileB.transform.position).Forget();

            await UniTask.Delay(TimeSpan.FromSeconds(0.3f), cancellationToken: _destroyCancellationToken);
            RemoveTiles(tileA, tileB);

            // 每对之间稍微延迟一下，让效果更明显
            if (pair != tilePairs.Last())
            {
                await UniTask.Delay(TimeSpan.FromSeconds(0.2f), cancellationToken: _destroyCancellationToken);
            }
        }

        OnMatchComplete();
    }
    #endregion

    public List<Tile3D> FindMatchGroup()
    {
        if (tileManager == null)
            return new List<Tile3D>();

        var matchablePair = tileManager.FindMatchablePair();
        if (matchablePair.HasValue)
        {
            var pos1 = matchablePair.Value.Item1;
            var pos2 = matchablePair.Value.Item2;
            return new List<Tile3D> { board.grid[pos1.x, pos1.y].tile, board.grid[pos2.x, pos2.y].tile };
        }
        return new List<Tile3D>();
    }

    #region 使用提示
    private Tile3D promptedTileA;
    private Tile3D promptedTileB;
    public bool PromptMatchGroup()
    {
        var matchedTiles = FindMatchGroup();
        if (matchedTiles.Count < 2)
            return false;
        promptedTileA = matchedTiles[0];
        promptedTileA.OnPrompt();

        promptedTileB = matchedTiles[1];
        promptedTileB.OnPrompt();
        return true;
    }

    private void UnpromptMatchGroup()
    {
        if (promptedTileA != null)
        {
            promptedTileA.OnUnprompt();
            promptedTileA = null;
        }
        if (promptedTileB != null)
        {
            promptedTileB.OnUnprompt();
            promptedTileB = null;
        }
    }
    #endregion

    private void AutoPromptMatch()
    {
        var matchablePairs = FindMatchGroup();
        if (matchablePairs.Count > 1)
        {
            matchablePairs[0].OnDoubleClick();
            matchablePairs[1].OnDoubleClick();
        }
    }

    /// <summary>
    /// 自动点击匹配的砖块
    /// </summary>
    private void AutoClickMatch()
    {
        var matchablePair = tileManager.FindMatchablePair();
        if (matchablePair.HasValue)
        {
            var posA = matchablePair.Value.Item1;
            var posB = matchablePair.Value.Item2;
            var tileA = board.grid[posA.x, posA.y].tile;
            var tileB = board.grid[posB.x, posB.y].tile;

            if (tileA != null && tileB != null)
            {
                // 如果已经有选中的砖块，先取消选中
                if (selectedTile != null)
                {
                    selectedTile.OnDeselect();
                    selectedTile = null;
                }

                // 先点击第一个砖块
                OnTileClick(tileA);

                // 等待一帧后点击第二个砖块
                DOTween.Sequence()
                    .AppendInterval(autoClickDelay / 2)
                    .AppendCallback(() =>
                    {
                        if (this != null && isAutoClickEnabled)
                        {
                            OnTileClick(tileB);
                        }
                    });
            }
        }
        // else if (!CheckSolution(false))
        // {
        //     // 如果没有可匹配的对子且无解，则尝试洗牌
        //     ShuffleTiles().Forget();
        // }
    }

    private async UniTaskVoid ShiftTilesCoroutine()
    {
        if (gravityDirection != GravityDirection.None)
        {
            isTileMoving = true;
            await movementController.ShiftTiles(tileManager, gravityDirection);
            isTileMoving = false;
        }
    }

    private void OpenSpecialTiles()
    {
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty)
                {
                    var tile = cell.tile;
                    if (tile != null && tile.type > Tile3D.TYPE_BLOCK_END)
                    {
                        PlayTileEffect(tile, true);
                        var needRemove = tile.RemoveSpecialTile();
                        if (needRemove)
                        {
                            tileManager.RemoveTile(cell.pos);
                        }
                    }
                }
            }
        }

        tileManager.RefreshTypeCache();
        CheckWin();
    }

    public bool UseItem(int itemId)
    {
        UpdateLastOperationTime();

        bool useSuccess = false;
        switch (itemId)
        {
            case ItemId.ItemCleanUp:
                useSuccess = CleanUpMatchGroup();
                break;
            case ItemId.ItemShuffle:
                ShuffleTiles().Forget();
                useSuccess = true;
                break;
            case ItemId.ItemPrompt:
                useSuccess = PromptMatchGroup();
                break;
        }
        return useSuccess;
    }

    #region 刷新逻辑
    public async UniTaskVoid ShuffleTiles()
    {
        if (selectedTile != null)
        {
            selectedTile.OnDeselect();
            selectedTile = null;
        }
        UnpromptMatchGroup();

        //洗牌，无解继续尝试
        bool hasMatch = false;
        for (int i = 0; i < 10; i++)
        {
            tileManager.ShuffleTiles();
            hasMatch = CheckSolution(false);
            if (hasMatch)
                break;
            Log.Info("=========无解，重新刷新=====");
        }

        if (!hasMatch)
        {
            Log.Info("======完全无解，不再刷新=====");
            OpenSpecialTiles();
        }
        SoundManager.PlayEffect(SoundPath.sfx_useItem_flushed);

        // 随机选择动画类型
        var animationType = 1;
        var sequence = DOTween.Sequence();
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty)
                {
                    var tile = cell.tile;
                    var targetPos = board.GridToWorldPosition(cell.pos);
                    var transform = tile.transform;
                    tile.StartShuffle();

                    switch (animationType)
                    {
                        case 0: // 翻转加弹跳
                            sequence.Join(transform.DOMove(targetPos + Vector3.up * 2f, 0.25f).SetEase(Ease.OutQuad));
                            sequence.Join(transform.DORotate(new Vector3(360, 360, 0), 0.5f, RotateMode.LocalAxisAdd));
                            sequence.Insert(x * y * 0.05f, transform.DOMove(targetPos, 0.25f).SetEase(Ease.InQuad).SetDelay(0.25f));
                            break;

                        case 1: // 缩放翻转
                            sequence.Join(transform.DOMove(targetPos, 0.5f).SetEase(Ease.OutBack));
                            sequence.Join(transform.DORotate(new Vector3(360, 360, 0), 0.5f, RotateMode.LocalAxisAdd));
                            break;
                    }
                }
            }
        }
        await sequence.AsyncWaitForCompletion().AsUniTask();

        if (this == null)
            return;
        for (int x = 1; x <= innerGridWidth; x++)
        {
            for (int y = 1; y <= innerGridHeight; y++)
            {
                var cell = board.grid[x, y];
                if (cell != null && !cell.IsEmpty)
                {
                    var tile = cell.tile;
                    tile.EndShuffle();
                }
            }
        }

        NextAutoClick().Forget();
    }
    #endregion

    private Tile3D GetTileAt(Vector2Int pos)
    {
        if (pos.x < 1 || pos.x > innerGridWidth || pos.y < 1 || pos.y > innerGridHeight)
        {
            return null;
        }

        var cell = board.grid[pos.x, pos.y];
        if (cell == null || cell.IsEmpty)
        {
            return null;
        }

        return cell.tile;
    }
}
