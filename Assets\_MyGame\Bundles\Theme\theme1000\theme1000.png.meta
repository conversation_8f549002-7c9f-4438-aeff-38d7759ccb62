fileFormatVersion: 2
guid: 2122d7f4e6e7f74449563f5c186eda53
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme1000_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f23b8cef61f5e9849a083fa5d8fbb7c7
      internalID: 988018933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 63d4ccfeae93f3140b7c4dcda8d4c549
      internalID: 584180718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f58a4c3929f9f474db3df0dd0749160b
      internalID: 1143571520
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e797199bc6c6b4d47ba94ed223967715
      internalID: -102515197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ea2b6e9fa94c5b4d892df14bfdade2a
      internalID: 918617157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ed879bd5980af146a9e077c4d6ddd25
      internalID: 991024462
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cfb8983c6f1de5b4b8340f1b90df68ce
      internalID: -1799320404
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0d6b985932d32794c8e02cefeb2a8228
      internalID: 637758305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9c956582d437f834896848f9e94064c3
      internalID: -345881186
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d00e11883de43146804158bb0b8508a
      internalID: -628892278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: abd004a18bbf7f245973ac31f78240e8
      internalID: 1559370004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5f5c4a4e04ff20545a3578883ddd0f8a
      internalID: -1285362389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 62503515bfa727c45bc9997d20c85d42
      internalID: 1811822806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3bba1eb87b6b1e4ab2e943445e3973f
      internalID: 223462290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8df4368107f5bd743b1c823c3b257764
      internalID: 1720520876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dc5b0ee316204634c82641f563bb2947
      internalID: -202314718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4f6360355171b54408212eb9a7f8b41a
      internalID: -920311197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6da672b748e9ee54a9137a590daa266d
      internalID: -1011045960
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 91f1130fa1dab724f9db0b58dde289e9
      internalID: -1855882495
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 67a126ce62cb48b4a814500e841b7d27
      internalID: 532313730
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c31f69e43ff2f4a4bbb9fa563ea3ec7b
      internalID: -1355248693
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8703aeafc0474d84eb506be2663f776d
      internalID: 204174100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94dc0d23684610c41b4ab9abf4e18aae
      internalID: 448910241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f938928fb80449b4493974fc1a55977a
      internalID: 1731569181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 554d9a15acbcfe548b9615f58b970180
      internalID: 1443865091
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bebc773c6e2e7884b81c5bd52163c862
      internalID: -749897740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3bd335ee3ece3524aa685f7244e0a871
      internalID: 1155205554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1d743758cf99d9246bbf37238e5c7dbd
      internalID: 1280749559
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 964fe189a1dd5e4469d3ccae501802d2
      internalID: -1341447916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0f501862d0414c3418a00f4cbf7c9b09
      internalID: 2684355
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 733503515a75e8242ad1836cff7a02f5
      internalID: 618686427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7c3dd3d2f68a8444ca174227b916e637
      internalID: 565110707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f455a5561a5b7f74cb7157eec56944fa
      internalID: -160783342
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d0e66ab24cadfa4db7c6a699fd68dd8
      internalID: -1705385628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 88a57994cba6d2d41bead3a536d879aa
      internalID: 262968466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1000_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d373b4356ee64147ae119cabf2b26a1
      internalID: 596206930
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 24b8373eafce8124aae0a8017cc5ed25
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme1000_30: 618686427
      theme1000_9: -628892278
      theme1000_32: -160783342
      theme1000_6: -1799320404
      theme1000_28: -1341447916
      theme1000_24: 1443865091
      theme1000_16: -920311197
      theme1000_7: 637758305
      theme1000_14: 1720520876
      theme1000_17: -1011045960
      theme1000_18: -1855882495
      theme1000_20: -1355248693
      theme1000_10: 1559370004
      theme1000_26: 1155205554
      theme1000_34: 262968466
      theme1000_25: -749897740
      theme1000_19: 532313730
      theme1000_21: 204174100
      theme1000_35: 596206930
      theme1000_23: 1731569181
      theme1000_33: -1705385628
      theme1000_13: 223462290
      theme1000_29: 2684355
      theme1000_22: 448910241
      theme1000_0: 988018933
      theme1000_3: -102515197
      theme1000_4: 918617157
      theme1000_27: 1280749559
      theme1000_15: -202314718
      theme1000_2: 1143571520
      theme1000_11: -1285362389
      theme1000_8: -345881186
      theme1000_12: 1811822806
      theme1000_5: 991024462
      theme1000_31: 565110707
      theme1000_1: 584180718
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
