using System.Collections.Generic;
using UnityEngine;
using LitJson;

/// <summary>
/// 折扣礼品奖励管理器
/// 负责管理每关的随机道具生成和存储
/// </summary>
public static class DiscountGiftRewardManager
{
    /// <summary>
    /// 获取当前关卡的折扣礼品奖励
    /// 如果当前关卡还没有生成过奖励，则随机生成并保存
    /// </summary>
    /// <returns>当前关卡的奖励列表</returns>
    public static ItemVo[] GetCurrentLevelRewards()
    {
        var currentLevelKey = GetCurrentLevelKey();
        var savedRewards = GetSavedRewards(currentLevelKey);
        
        if (savedRewards != null && savedRewards.Length > 0)
        {
            // 已经生成过奖励，直接返回
            return savedRewards;
        }
        
        // 还没有生成过奖励，随机生成
        var newRewards = GenerateRandomRewards();
        SaveRewards(currentLevelKey, newRewards);
        
        return newRewards;
    }
    
    /// <summary>
    /// 生成随机奖励
    /// </summary>
    /// <returns>随机生成的奖励列表</returns>
    private static ItemVo[] GenerateRandomRewards()
    {
        var rewardCount = ConfigSetting.discountGiftRewardCount;
        var rewards = new ItemVo[rewardCount];
        
        for (int i = 0; i < rewardCount; i++)
        {
            var drop = ConfigDropItem.GetData(DropIds.DiscountGift);
            if (drop != null)
            {
                rewards[i] = new ItemVo(drop.itemId, drop.count);
            }
            else
            {
                // 如果配置有问题，给默认道具
                rewards[i] = new ItemVo(ItemId.ItemPrompt, 1);
            }
        }
        
        return rewards;
    }
    
    /// <summary>
    /// 获取当前关卡的唯一标识
    /// </summary>
    /// <returns>关卡标识字符串</returns>
    private static string GetCurrentLevelKey()
    {
        return $"{GameGlobal.EnterLevel}_{(int)GameGlobal.BattleType}";
    }
    
    /// <summary>
    /// 从存储中获取已保存的奖励
    /// </summary>
    /// <param name="levelKey">关卡标识</param>
    /// <returns>已保存的奖励列表，如果没有则返回null</returns>
    private static ItemVo[] GetSavedRewards(string levelKey)
    {
        var savedData = StorageMgr.DiscountGiftCurrentLevelRewards;
        if (string.IsNullOrEmpty(savedData))
        {
            return null;
        }
        
        try
        {
            var rewardData = JsonMapper.ToObject(savedData);
            if (rewardData == null || !rewardData.ContainsKey(levelKey))
            {
                return null;
            }
            
            var levelRewards = rewardData[levelKey];
            if (levelRewards == null || !levelRewards.IsArray)
            {
                return null;
            }
            
            var rewards = new List<ItemVo>();
            for (int i = 0; i < levelRewards.Count; i++)
            {
                var item = levelRewards[i];
                if (item.ContainsKey("itemId") && item.ContainsKey("count"))
                {
                    var itemId = JsonUtil.ToInt(item, "itemId");
                    var count = JsonUtil.ToInt(item, "count");
                    rewards.Add(new ItemVo(itemId, count));
                }
            }
            
            return rewards.ToArray();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"解析折扣礼品奖励数据失败: {e.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// 保存奖励到存储
    /// </summary>
    /// <param name="levelKey">关卡标识</param>
    /// <param name="rewards">要保存的奖励列表</param>
    private static void SaveRewards(string levelKey, ItemVo[] rewards)
    {
        try
        {
            var savedData = StorageMgr.DiscountGiftCurrentLevelRewards;
            JsonData rewardData;
            
            if (string.IsNullOrEmpty(savedData))
            {
                rewardData = new JsonData();
                rewardData.SetJsonType(JsonType.Object);
            }
            else
            {
                rewardData = JsonMapper.ToObject(savedData);
            }
            
            // 创建当前关卡的奖励数组
            var levelRewards = new JsonData();
            levelRewards.SetJsonType(JsonType.Array);
            
            foreach (var reward in rewards)
            {
                var itemData = new JsonData();
                itemData.SetJsonType(JsonType.Object);
                itemData["itemId"] = reward.itemId;
                itemData["count"] = reward.count;
                levelRewards.Add(itemData);
            }
            
            rewardData[levelKey] = levelRewards;
            
            // 保存到存储
            StorageMgr.DiscountGiftCurrentLevelRewards = JsonMapper.ToJson(rewardData);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存折扣礼品奖励数据失败: {e.Message}");
        }
    }
    
    /// <summary>
    /// 清理过期的奖励数据（可选，用于节省存储空间）
    /// </summary>
    public static void CleanupOldRewards()
    {
        try
        {
            var savedData = StorageMgr.DiscountGiftCurrentLevelRewards;
            if (string.IsNullOrEmpty(savedData))
            {
                return;
            }
            
            var rewardData = JsonMapper.ToObject(savedData);
            var currentLevelKey = GetCurrentLevelKey();
            var keysToRemove = new List<string>();
            
            // 找出所有不是当前关卡的数据
            foreach (string key in rewardData.Keys)
            {
                if (key != currentLevelKey)
                {
                    keysToRemove.Add(key);
                }
            }
            
            // 移除过期数据
            foreach (var key in keysToRemove)
            {
                rewardData.Remove(key);
            }
            
            // 保存清理后的数据
            StorageMgr.DiscountGiftCurrentLevelRewards = JsonMapper.ToJson(rewardData);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"清理折扣礼品奖励数据失败: {e.Message}");
        }
    }
}
