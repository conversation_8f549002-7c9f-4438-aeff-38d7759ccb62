using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 折扣礼品奖励管理器
/// 负责管理每关的随机道具生成（仅在当前游戏生命周期内有效）
/// </summary>
public static class DiscountGiftRewardManager
{
    private static ItemVo[] rewards;
    /// <summary>
    /// 获取当前关卡的折扣礼品奖励
    /// 如果当前关卡还没有生成过奖励，则随机生成并缓存
    /// </summary>
    /// <returns>当前关卡的奖励列表</returns>
    public static ItemVo[] GetCurrentLevelRewards()
    {
        if (rewards == null)
        {
            rewards = GenerateRandomRewards();
        }
        return rewards;
    }

    /// <summary>
    /// 生成随机奖励
    /// </summary>
    /// <returns>随机生成的奖励列表</returns>
    private static ItemVo[] GenerateRandomRewards()
    {
        var rewardCount = ConfigSetting.discountGiftRewardCount;
        var rewards = new ItemVo[rewardCount];

        for (int i = 0; i < rewardCount; i++)
        {
            var drop = ConfigDropItem.GetData(DropIds.DiscountGift);
            if (drop != null)
            {
                rewards[i] = new ItemVo(drop.itemId, drop.count);
            }
            else
            {
                // 如果配置有问题，给默认道具
                rewards[i] = new ItemVo(ItemId.ItemPrompt, 1);
            }
        }

        return rewards;
    }


    /// <summary>
    /// 清理所有缓存的奖励数据（可选，用于释放内存）
    /// </summary>
    public static void ClearCache()
    {
        rewards = null;
    }
}
