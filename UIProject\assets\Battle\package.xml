<?xml version="1.0" encoding="utf-8"?>
<packageDescription id="kzjqd5ah">
  <resources>
    <component id="jspm19" name="SettingPanel.xml" path="/" exported="true"/>
    <component id="qoqy1p" name="TapMsg.xml" path="/"/>
    <component id="qoqy1r" name="Hand.xml" path="/"/>
    <component id="qoqy1s" name="GuideMask.xml" path="/"/>
    <component id="wupj1v" name="PrgResult.xml" path="/"/>
    <image id="fs2p27" name="hand1.png" path="/images/"/>
    <image id="fs2p28" name="hand2.png" path="/images/"/>
    <component id="fs2p2a" name="MouseHand.xml" path="/" exported="true"/>
    <component id="g23930" name="Coin.xml" path="/" exported="true"/>
    <component id="mr9u36" name="LeftTimeBar.xml" path="/"/>
    <component id="mr9u37" name="StarBar.xml" path="/"/>
    <image id="mr9u38" name="star.png" path="/images/"/>
    <component id="oak13e" name="btnContinueByVideoY.xml" path="/"/>
    <component id="oak13g" name="ResultWinPanel.xml" path="/" exported="true"/>
    <component id="cqln16" name="btnShare.xml" path="/" exported="true"/>
    <component id="cqln3t" name="btnMultReward.xml" path="/" exported="true"/>
    <image id="cqln3u" name="coin.png" path="/images/"/>
    <component id="givr3y" name="BuyItemPanel.xml" path="/" exported="true"/>
    <image id="ss2k44" name="levelBg.png" path="/images/" scale="9grid" scale9grid="17,19,6,5"/>
    <component id="ss2k46" name="btnPause.xml" path="/"/>
    <image id="cpy347" name="btnAdd.png" path="/images/"/>
    <image id="cpy349" name="itemNumBg.png" path="/images/" scale="9grid" scale9grid="27,0,1,55"/>
    <component id="cpy34j" name="btnVibrate.xml" path="/"/>
    <image id="t5i14s" name="image_countBg.png" path="/images/"/>
    <image id="t5i14v" name="font_combo_00.png" path="/fonts/"/>
    <image id="t5i14w" name="font_combo_01.png" path="/fonts/"/>
    <image id="t5i14x" name="font_combo_02.png" path="/fonts/"/>
    <image id="t5i14y" name="font_combo_03.png" path="/fonts/"/>
    <image id="t5i14z" name="font_combo_04.png" path="/fonts/"/>
    <image id="t5i150" name="font_combo_05.png" path="/fonts/"/>
    <image id="t5i151" name="font_combo_06.png" path="/fonts/"/>
    <image id="t5i152" name="font_combo_07.png" path="/fonts/"/>
    <image id="t5i153" name="font_combo_08.png" path="/fonts/"/>
    <image id="t5i154" name="font_combo_09.png" path="/fonts/"/>
    <image id="t5i157" name="image_bg.png" path="/images/" scale="9grid" scale9grid="20,19,6,8" atlas="1"/>
    <image id="t5i15c" name="coinBig.png" path="/images/"/>
    <image id="t5i15d" name="image_heart.png" path="/images/"/>
    <image id="t5i15e" name="image_line.png" path="/images/" scale="tile"/>
    <image id="t5i15k" name="image_btnIcon_ad.png" path="/images/"/>
    <image id="t5i15n" name="prg_result_bar.png" path="/images/"/>
    <image id="t5i15o" name="prg_result_bg.png" path="/images/" scale="9grid" scale9grid="13,0,2,26"/>
    <component id="ux5k5p" name="btnContinueFree.xml" path="/"/>
    <image id="ux5k5q" name="btn_remove_title1.png" path="/images/" exported="true"/>
    <image id="ux5k5r" name="btn_buyRemove_title1.png" path="/images/" exported="true"/>
    <image id="s9fh5s" name="btn_buyRemove_title2.png" path="/images/" exported="true"/>
    <image id="s9fh5t" name="btn_remove_title2.png" path="/images/" exported="true"/>
    <image id="iz8v5x" name="image_tips_arrow.png" path="/images/"/>
    <image id="iz8v5y" name="image_tips_bg.png" path="/images/" scale="9grid" scale9grid="36,31,5,13"/>
    <component id="iz8v60" name="ItemTips.xml" path="/"/>
    <image id="dvx263" name="prg_sound_bg.png" path="/images/" scale="9grid" scale9grid="19,0,5,36"/>
    <image id="dvx266" name="prg_sound_bar2.png" path="/images/" scale="9grid" scale9grid="18,0,7,37"/>
    <image id="dvx267" name="123.png" path="/images/"/>
    <image id="dvx26b" name="image_icon_bgm.png" path="/images/"/>
    <image id="dvx26c" name="image_icon_vibration.png" path="/images/"/>
    <image id="dvx26d" name="image_icon_sfx.png" path="/images/"/>
    <image id="kery6t" name="image_itemBg.png" path="/images/"/>
    <component id="s4ex7a" name="btnGetHeart.xml" path="/"/>
    <component id="s4ex7b" name="ResultContinue.xml" path="/" exported="true"/>
    <component id="s4ex7c" name="btnCom.xml" path="/"/>
    <image id="s4ex7d" name="bg_blue.png" path="/images/" scale="9grid" scale9grid="52,0,6,114"/>
    <image id="s4ex7e" name="btn_text_not.png" path="/images/"/>
    <image id="o6rc7i" name="124.png" path="/images/"/>
    <image id="o6rc7k" name="image_passTipsBg.png" path="/images/" scale="9grid" scale9grid="174,32,7,15"/>
    <image id="bvh47n" name="bg_yellow.png" path="/images/" scale="9grid" scale9grid="56,0,12,125"/>
    <image id="mxgl7r" name="btn_text_free2.png" path="/images/"/>
    <image id="mxgl7s" name="image_line3.png" path="/images/" scale="9grid" scale9grid="0,2,3,2"/>
    <image id="vomt8c" name="prg_title_bg.png" path="/images/" scale="9grid" scale9grid="15,0,4,30"/>
    <image id="vomt8d" name="prg_title_bar.png" path="/images/" scale="9grid" scale9grid="11,0,3,22"/>
    <component id="vomt8e" name="PrgTitle.xml" path="/"/>
    <image id="vomt8f" name="image_passTimeBg.png" path="/images/" scale="9grid" scale9grid="33,0,9,64"/>
    <image id="vomt8h" name="btn_switch.png" path="/images/" scale9grid="26,0,6,45"/>
    <image id="bj6u8m" name="125.png" path="/images/"/>
    <image id="bj6u8p" name="126.png" path="/images/"/>
    <component id="ecla8q" name="BattlePanel.xml" path="/" exported="true"/>
    <image id="ecla8r" name="image_timeBg.png" path="/images/" scale="9grid" scale9grid="22,0,9,45"/>
    <image id="ecla8s" name="image_gateBg.png" path="/images/" scale="9grid" scale9grid="25,26,7,5"/>
    <component id="ecla8u" name="BtnItem.xml" path="/"/>
    <image id="ecla8x" name="image_btnItemBg.png" path="/images/" scale="9grid" scale9grid="50,33,29,41"/>
    <image id="ecla8y" name="icon_tips.png" path="/images/"/>
    <image id="ecla8z" name="image_itemNameBg.png" path="/images/" scale="9grid" scale9grid="32,33,6,4"/>
    <image id="ecla91" name="icon_remove.png" path="/images/"/>
    <image id="ecla93" name="icon_shuffle.png" path="/images/"/>
    <image id="ecla94" name="btnText_shuffle.png" path="/images/"/>
    <image id="ecla95" name="btnText_clean.png" path="/images/"/>
    <component id="jtwm96" name="GuideComp.xml" path="/" exported="true"/>
    <component id="op5o97" name="GateEntryPanel.xml" path="/" exported="true"/>
    <image id="op5o98" name="arrow1.png" path="/images/" exported="true"/>
    <component id="thvn99" name="LifeCom.xml" path="/"/>
    <image id="ivme9a" name="arrow2.png" path="/images/" exported="true"/>
    <image id="ivme9b" name="arrow3.png" path="/images/" exported="true"/>
    <image id="ivme9c" name="arrow4.png" path="/images/" exported="true"/>
    <image id="ivme9d" name="arrow5.png" path="/images/" exported="true"/>
    <image id="ivme9e" name="arrow6.png" path="/images/" exported="true"/>
    <image id="ivme9f" name="arrow7.png" path="/images/" exported="true"/>
    <image id="ivme9g" name="arrow8.png" path="/images/" exported="true"/>
    <image id="ivme9h" name="arrow9.png" path="/images/" exported="true"/>
    <component id="d0mn9i" name="ResultDailyWinPanel.xml" path="/" exported="true"/>
    <component id="912v9j" name="ResultCollectPanel.xml" path="/" exported="true"/>
    <image id="912v9l" name="image_bg2.png" path="/images/" scale="9grid" scale9grid="11,10,5,7"/>
    <image id="912v9m" name="image_new.png" path="/images/"/>
    <image id="912v9n" name="btn_switchTheme.png" path="/images/" scale="9grid" scale9grid="16,16,6,5"/>
    <image id="912v9p" name="prg_collect_bar.png" path="/images/" scale="9grid" scale9grid="13,0,6,28"/>
    <image id="912v9q" name="prg_collect_bg.png" path="/images/" scale="9grid" scale9grid="17,0,5,34"/>
    <component id="912v9r" name="ListPatternIcon.xml" path="/"/>
    <component id="912v9s" name="PrgCollectTheme.xml" path="/"/>
    <component id="912v9t" name="ResultStarPanel.xml" path="/" exported="true"/>
    <component id="912v9w" name="ResultFailRefresh.xml" path="/" exported="true"/>
    <component id="912v9x" name="GiveUpPanel.xml" path="/" exported="true"/>
    <component id="912v9z" name="ResultRankChange.xml" path="/" exported="true"/>
    <image id="912va0" name="image_rankingChange.png" path="/images/"/>
    <component id="ku5ia1" name="ThemeIcon.xml" path="/"/>
    <component id="ctpua2" name="btnArrow.xml" path="/"/>
    <component id="rhiya3" name="ComboEffect.xml" path="/" exported="true"/>
    <image id="rhiya4" name="combo1.png" path="/combo/" exported="true"/>
    <image id="rhiya5" name="combo5.png" path="/combo/" exported="true"/>
    <image id="rhiya6" name="combo3.png" path="/combo/" exported="true"/>
    <image id="rhiya7" name="combo4.png" path="/combo/" exported="true"/>
    <image id="rhiya8" name="combo2.png" path="/combo/" exported="true"/>
    <image id="rhiya9" name="combo6.png" path="/combo/" exported="true"/>
    <image id="tfxzaa" name="arrow100.png" path="/images/" exported="true"/>
    <image id="tfxzab" name="arrow101.png" path="/images/" exported="true"/>
    <image id="tfxzac" name="arrow102.png" path="/images/" exported="true"/>
    <image id="tfxzad" name="arrow103.png" path="/images/" exported="true"/>
    <image id="tfxzae" name="arrow104.png" path="/images/" exported="true"/>
    <image id="tfxzaf" name="arrow105.png" path="/images/" exported="true"/>
    <component id="yrwzag" name="Star.xml" path="/" exported="true"/>
    <image id="mfj7aj" name="image_box.png" path="/images/"/>
    <component id="mfj7ak" name="PrgGate.xml" path="/"/>
    <image id="mfj7al" name="btnSet.png" path="/images/"/>
    <component id="fe0rau" name="ListReward.xml" path="/"/>
    <image id="iw0fav" name="image_itemBg2.png" path="/images/"/>
    <image id="ca55ax" name="image_entryBg.png" path="/images/" scale="9grid" scale9grid="38,0,5,74"/>
    <image id="ca55ay" name="image_light.png" path="/images/"/>
    <image id="ca55az" name="prg_title2_bar.png" path="/images/" scale="9grid" scale9grid="11,0,4,19"/>
    <image id="ca55b0" name="prg_title2_bg.png" path="/images/" scale="9grid" scale9grid="15,0,4,25"/>
    <image id="ca55b1" name="btnText_tips.png" path="/images/"/>
    <image id="hqjgb3" name="127.png" path="/images/"/>
    <image id="hqjgb4" name="128.png" path="/images/"/>
    <image id="hqjgb5" name="129.png" path="/images/"/>
    <misc id="hqjgb6" name="ShengLiBT.atlas" path="/spine/"/>
    <image id="hqjgb7" name="ShengLiBT.png" path="/spine/"/>
    <spine id="hqjgb8" name="ShengLiBT.skel" path="/spine/" width="449" height="147" require="hqjgb6,hqjgb7" atlasNames="ShengLiBT" anchor="228,81" shader="FairyGUI/Image"/>
    <image id="hqjgb9" name="image_timeBg2.png" path="/images/" scale="9grid" scale9grid="25,0,5,49"/>
    <image id="hqjgba" name="image_clock.png" path="/images/"/>
    <misc id="hqjgbb" name="ShengLiDiGuang.atlas" path="/spine/"/>
    <image id="hqjgbc" name="ShengLiDiGuang.png" path="/spine/"/>
    <spine id="hqjgbd" name="ShengLiDiGuang.skel" path="/spine/" width="647" height="661" require="hqjgbb,hqjgbc" atlasNames="ShengLiDiGuang" anchor="323,329" shader="FairyGUI/Image"/>
    <image id="hqjgbe" name="image_title.png" path="/images/"/>
    <component id="hqjgbf" name="BtnSwitchTheme.xml" path="/"/>
    <image id="hqjgbg" name="image_themeBg.png" path="/images/" scale="9grid" scale9grid="31,32,5,2"/>
    <image id="hqjgbh" name="130.png" path="/images/"/>
    <image id="hqjgbi" name="btn_yellowMin.png" path="/images/" scale="9grid" scale9grid="53,0,4,98"/>
    <component id="hqjgbj" name="BtnUse.xml" path="/"/>
    <image id="hqjgbk" name="image_prg_dec.png" path="/images/"/>
    <image id="hqjgbl" name="image_mask.png" path="/images/"/>
    <component id="hqjgbm" name="EffectHammer.xml" path="/" exported="true"/>
    <misc id="hqjgbn" name="CuiZi.atlas" path="/spine/"/>
    <image id="hqjgbo" name="CuiZi.png" path="/spine/"/>
    <spine id="hqjgbp" name="CuiZi.skel" path="/spine/" width="256" height="256" require="hqjgbn,hqjgbo" atlasNames="CuiZi" anchor="128,122" shader="FairyGUI/Image"/>
    <misc id="xigmbq" name="FangQiTiaoZhan.atlas" path="/spine/"/>
    <image id="xigmbr" name="FangQiTiaoZhan.png" path="/spine/"/>
    <misc id="xigmbs" name="HuoDeXingXing.atlas" path="/spine/"/>
    <image id="xigmbt" name="HuoDeXingXing.png" path="/spine/"/>
    <spine id="xigmbu" name="FangQiTiaoZhan.skel" path="/spine/" width="354" height="353" require="xigmbq,xigmbr" atlasNames="FangQiTiaoZhan" anchor="160,293" shader="FairyGUI/Image"/>
    <spine id="xigmbv" name="HuoDeXingXing.skel" path="/spine/" width="578" height="675" require="xigmbs,xigmbt" atlasNames="HuoDeXingXing" anchor="285,511" shader="FairyGUI/Image"/>
    <image id="xigmbw" name="image_hand.png" path="/images/" scale="9grid" scale9grid="75,0,4,45"/>
    <component id="xigmby" name="BtnGoCity.xml" path="/"/>
    <image id="iheibz" name="image_rankUpTitle.png" path="/images/"/>
    <image id="iheic0" name="image_arrow.png" path="/images/"/>
    <image id="iheic1" name="image_rankBg.png" path="/images/" scale="9grid" scale9grid="26,0,12,51"/>
    <image id="iheic3" name="131.png" path="/images/"/>
    <image id="k4hyc4" name="image_btnAds.png" path="/images/"/>
    <component id="ssvyc5" name="BtnItemTips.xml" path="/"/>
    <image id="ssvyc6" name="btnText_tips2.png" path="/images/"/>
    <component id="ip8mc7" name="BtnCom2.xml" path="/"/>
    <image id="ip8mc8" name="btn_addDesktop.png" path="/images/"/>
    <image id="ip8mc9" name="btn_invite.png" path="/images/"/>
    <image id="ip8mca" name="btn_addDesktopDY.png" path="/images/"/>
    <image id="k49ccb" name="image_btnItemBg2.png" path="/images/" scale="9grid" scale9grid="30,18,14,10"/>
    <image id="vlmwcc" name="btn_dyFeed.png" path="/images/"/>
    <image id="iypqcd" name="btn_backLobby.png" path="/images/"/>
    <component id="ow6rce" name="GateBoxTips.xml" path="/" exported="true"/>
    <component id="ow6rcf" name="TipsListItem.xml" path="/"/>
    <image id="ow6rcg" name="image_mark.png" path="/images/"/>
    <component id="ow6rch" name="BtnGateBox.xml" path="/"/>
    <image id="ow6rci" name="image_mark2.png" path="/images/"/>
    <component id="k5up98" name="BtnTest.xml" path="/"/>
    <component id="k5up99" name="TestPanel.xml" path="/" exported="true"/>
    <component id="k5upcj" name="BtnTestMenu.xml" path="/"/>
    <image id="k5upck" name="btn_com.png" path="/images/" scale="9grid" scale9grid="40,39,5,5"/>
    <image id="v6drcl" name="btnText_clean3.png" path="/images/"/>
    <image id="v6drcm" name="btnText_clean2.png" path="/images/"/>
    <component id="v6drcn" name="ClickGuideTips.xml" path="/" exported="true"/>
    <image id="v6drco" name="image_tips_bg2.png" path="/images/" scale="9grid" scale9grid="37,36,5,9"/>
    <image id="v6drcp" name="image_tips_arrow2.png" path="/images/"/>
    <image id="b7gbcq" name="btn_discountGift.png" path="/images/"/>
    <component id="b7gbcr" name="BtnDiscountGift.xml" path="/" exported="true"/>
    <component id="b7gbcs" name="DiscountGiftPanel.xml" path="/" exported="true"/>
    <image id="b7gbct" name="image_shu.png" path="/images/"/>
    <misc id="r2rzcu" name="QiQiuCangShu.atlas" path="/spine/"/>
    <image id="r2rzcv" name="QiQiuCangShu.png" path="/spine/"/>
    <spine id="r2rzcw" name="QiQiuCangShu.skel" path="/spine/" width="170" height="360" require="r2rzcu,r2rzcv" atlasNames="QiQiuCangShu" anchor="85,269" shader="FairyGUI/Image"/>
    <image id="r2rzcx" name="132.png" path="/images/"/>
    <image id="r2rzcy" name="image_gifts.png" path="/images/"/>
    <image id="r2rzcz" name="image_timeBg3.png" path="/images/" scale="9grid" scale9grid="18,0,7,36"/>
    <component id="j9avd0" name="ListItemGift.xml" path="/"/>
  </resources>
  <publish name=""/>
</packageDescription>