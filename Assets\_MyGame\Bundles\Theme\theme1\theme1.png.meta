fileFormatVersion: 2
guid: cd19ff9c4b4c5fc45a93e0042469c5e6
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 40cfd2d579659474d9f2e6de842565bd
      internalID: -2059514662
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d198a8f0fd49bf8459544b04e8b41690
      internalID: -540385487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7b92bbb314f1e643adc7ce9d217550e
      internalID: 1117961664
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a8c975783a21d1e4187ca62639540052
      internalID: -1051820113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5a2df384d0eecd54080e994d867c97bd
      internalID: 1655623460
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e96aafa6601b1cd46a12b043f0d500ef
      internalID: -1125003935
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 298e0fce6d045834aaf4a4517603c36f
      internalID: 1684655241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 460739a868b2a954690e751420e7905c
      internalID: -1119863090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 52748ff860300ff4ba05b331fcda6245
      internalID: 861832991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 26127ad4c22584642b7cace740e629e3
      internalID: -1265462408
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 99aa9c527f4f58b4ea879be89b686607
      internalID: 2064034797
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: de605a70b3f3b2a44850c43ddc18bba0
      internalID: 1134791419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 78c094db2e8a827478fd77600ba319c8
      internalID: -829035190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3f2f6539b5b9f844877bc738142e73f
      internalID: 502606934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3a5340fa2f032df49a55d9a29783c6d1
      internalID: -1512420927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c19c487fa410cc6479bee04899c6c647
      internalID: -1669891268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d2cf372e851137a4cbefc1d9348f386b
      internalID: -1601969106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6244dfa5b9d53dd4e85c4bd213d236c5
      internalID: 454028610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 32a6d3d4126505d4fb357ae317a21143
      internalID: -157953620
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4622d730e9fb9374e8b957c76c36f67f
      internalID: 1240548314
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3ed8982e7492cd44987a887bdbcaa9f
      internalID: 781611805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b586b902413014040ab764ec47510b98
      internalID: 1914471959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4d7c2096de4326b4186a9b2d6168a245
      internalID: -1033945838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 610226be78557dc4f9c98835c1ee78c9
      internalID: 1057417230
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 272437b3ef73bc041b5dbb605ad7ebd7
      internalID: 1711231284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71fcb3c5bf3c499419d1b00b19e6a4ae
      internalID: -2117523812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d905770b30e961b41a57d1d7a0df4dd7
      internalID: 435632125
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 356cb1f2ab00e6c488fb2a3c4381bbb8
      internalID: 1153823385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b4f7a5976d7f15e44a603c77d0bac331
      internalID: 1658417376
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 364c79172c2ea5049912eb2064696e59
      internalID: -275916546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b5dd35f333875d9438580aaa451f4a28
      internalID: 1197619815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f13bdf3512974224fadde18669ec0485
      internalID: -964345353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 09e4f275e8a0493489b9da42cd093812
      internalID: -672409446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4422be55526a83c4f991501eef0c0f51
      internalID: -1966784796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4e9bb4e9b4b06954b96bb8dc2ceeaa00
      internalID: -1015497366
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme1_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 713fdd4448ca002498cccc659c736424
      internalID: 2145202603
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme1_1: -540385487
      theme1_3: -1051820113
      theme1_19: 1240548314
      theme1_0: -2059514662
      theme1_15: -1669891268
      theme1_30: 1197619815
      theme1_7: -1119863090
      theme1_12: -829035190
      theme1_16: -1601969106
      theme1_22: -1033945838
      theme1_28: 1658417376
      theme1_32: -672409446
      theme1_4: 1655623460
      theme1_13: 502606934
      theme1_31: -964345353
      theme1_2: 1117961664
      theme1_6: 1684655241
      theme1_10: 2064034797
      theme1_23: 1057417230
      theme1_14: -1512420927
      theme1_20: 781611805
      theme1_21: 1914471959
      theme1_24: 1711231284
      theme1_27: 1153823385
      theme1_8: 861832991
      theme1_11: 1134791419
      theme1_18: -157953620
      theme1_33: -1966784796
      theme1_17: 454028610
      theme1_34: -1015497366
      theme1_25: -2117523812
      theme1_26: 435632125
      theme1_35: 2145202603
      theme1_9: -1265462408
      theme1_5: -1125003935
      theme1_29: -275916546
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
