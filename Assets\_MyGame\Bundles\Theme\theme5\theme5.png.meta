fileFormatVersion: 2
guid: 6e93cc8c7e4c37e4b9b4c12c65ded9d6
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme5_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3376c6719b15ae4e873ce02d2df41e9
      internalID: -1072857640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a7cb8dcebb1662a4c9f19147a42835b7
      internalID: 395792927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8fed5e07dfb58c24eb0b9162112c4c10
      internalID: -1426074878
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ab433a498d499b04f9216dee3f0dcbe4
      internalID: -714551446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38e5bbed6eae3b549b01ca3467f01c8b
      internalID: -602463470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2d9f0a86bd8a44b4fb5b8e5bfe6bc208
      internalID: -219061387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f98e98d9a8b93cd46bbc126e860ff19b
      internalID: -1251770817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34b2018d2332f534ba6383fc839b1445
      internalID: 325211530
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7260f95e52863214b9ba4a74581c8165
      internalID: 1118728636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 063e665f2a7576b4db03923eb0ff5f51
      internalID: 354766640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4e2e4f6a5a94d9f4faf0fdef5fa50b16
      internalID: -31740961
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd8b51bd509a0a34289a4f1420ad6725
      internalID: 1541927698
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1eeee4872957eb448cbddd754b4147d
      internalID: -719888855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8b5e71bfcebe7ca45b3539f6f681476e
      internalID: 868763570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da335f6ec15b7b44a915028d8514c556
      internalID: -1349163753
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8aa971e06ec61f945b8f389fbb48b43b
      internalID: 320032508
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5267b4472ed0a0a4382be5ebdaba7211
      internalID: 1585965817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d0790507d8374d499edde928555712e
      internalID: 2029788196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9e108e3ce8278744997c35d62c3ccccd
      internalID: 1265896076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 12ad57c067aaa5c4298558a4d8a534d2
      internalID: -1468499932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3a911c679407fbc4995489e33fcaf4ea
      internalID: -1726883952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 970042ee409ab794d8968fc22344c49b
      internalID: 1283291026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f22afb6081bb9494fa7f943f272e6f92
      internalID: -393201118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4492eb51f4b2faf45aba21d20669f4e3
      internalID: 1709030258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ebae8810b6c942b48a4ef8938f67f34b
      internalID: -809389259
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b0ba51bfa1b271445983ce615a81cf43
      internalID: -689944175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7bf88a1bd8a8b5c46a9fc338d51de59f
      internalID: 1672651349
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3f06bda99f3485146b75c1caf898b110
      internalID: 1887965679
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c428d4f2de289e0488a61de291753075
      internalID: 630058088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1bf11978edc7f7947aa23ebd7c5a2741
      internalID: -1786714585
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 514ee2f99d030ab47b9ac3b69718093c
      internalID: -1092963478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b7889e143040ad649a70b8df7d41aa7c
      internalID: 568254390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68d8234e99c75af4ba8236978d51e89f
      internalID: -162670440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3c053e797d759ee449ac3c665e0b8d6e
      internalID: 1857343217
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24b9517db1ebc22489b92945d4416009
      internalID: -890646628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme5_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b08eb4f18ad7ec14a9c9d72bc886a9fb
      internalID: -1073631007
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 02eb6320ee9cfd348a5476dd5860e7f6
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme5_1: 395792927
      theme5_18: 1265896076
      theme5_22: -393201118
      theme5_27: 1887965679
      theme5_31: 568254390
      theme5_5: -219061387
      theme5_16: 1585965817
      theme5_26: 1672651349
      theme5_25: -689944175
      theme5_4: -602463470
      theme5_30: -1092963478
      theme5_3: -714551446
      theme5_19: -1468499932
      theme5_12: -719888855
      theme5_17: 2029788196
      theme5_15: 320032508
      theme5_24: -809389259
      theme5_7: 325211530
      theme5_28: 630058088
      theme5_33: 1857343217
      theme5_6: -1251770817
      theme5_11: 1541927698
      theme5_34: -890646628
      theme5_21: 1283291026
      theme5_9: 354766640
      theme5_20: -1726883952
      theme5_14: -1349163753
      theme5_23: 1709030258
      theme5_29: -1786714585
      theme5_2: -1426074878
      theme5_8: 1118728636
      theme5_32: -162670440
      theme5_13: 868763570
      theme5_10: -31740961
      theme5_0: -1072857640
      theme5_35: -1073631007
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
