fileFormatVersion: 2
guid: f31e4bf889d44e84199ab945ed537062
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: theme4_0
      rect:
        serializedVersion: 2
        x: 0
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 899caee2099c7cf4e883ea853ef51185
      internalID: -1214504659
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_1
      rect:
        serializedVersion: 2
        x: 170
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f7413a1769e66fc46a86ce1fdddcd361
      internalID: 159631622
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_2
      rect:
        serializedVersion: 2
        x: 340
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 364198829cd468c4c9915ca2d7d1ff52
      internalID: -1491330017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_3
      rect:
        serializedVersion: 2
        x: 510
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38be07aadd2f28741bb7087e58dc390e
      internalID: 1586977058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_4
      rect:
        serializedVersion: 2
        x: 680
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 228fa548315cc814e90b31b7c0034d53
      internalID: 1954813571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_5
      rect:
        serializedVersion: 2
        x: 850
        y: 853.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7bccc42f5f8504546b20cebaf6166b20
      internalID: 237620543
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_6
      rect:
        serializedVersion: 2
        x: 0
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7f6eff7e5ec8da649ae1ca766c3c7ca7
      internalID: 1520430069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_7
      rect:
        serializedVersion: 2
        x: 170
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8043d017b16727040bd6091b5fdac134
      internalID: -810876210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_8
      rect:
        serializedVersion: 2
        x: 340
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0cc7afe5e5f4f5a4daeb2008a319408c
      internalID: 302751509
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_9
      rect:
        serializedVersion: 2
        x: 510
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 536b2b61679ebc04f87e1b26c19612d0
      internalID: -139452264
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_10
      rect:
        serializedVersion: 2
        x: 680
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9657eacb8e890d84783c22e463545e36
      internalID: -418266704
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_11
      rect:
        serializedVersion: 2
        x: 850
        y: 683.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 920f9063e4f618147b514db5619479c9
      internalID: -1175288546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_12
      rect:
        serializedVersion: 2
        x: 0
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 71bac780c15a70a44bbc31429fa5dc6a
      internalID: -1046997238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_13
      rect:
        serializedVersion: 2
        x: 170
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: caf8372fdf43b4f4ca7746059b2765b7
      internalID: -1678766380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_14
      rect:
        serializedVersion: 2
        x: 340
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9558981a9c0f6554d9e2c9dd1bc17764
      internalID: -1739448068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_15
      rect:
        serializedVersion: 2
        x: 510
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 425c50c02028b124688e86e9aed23076
      internalID: -1572735854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_16
      rect:
        serializedVersion: 2
        x: 680
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 20da4dcc82b10104c8d1499c4306047f
      internalID: 372247162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_17
      rect:
        serializedVersion: 2
        x: 850
        y: 513.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b6c78754241f8f2458498f19b984bd0a
      internalID: -2060796729
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_18
      rect:
        serializedVersion: 2
        x: 0
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 118c5a90887f54442ae95ab0ffa4b06f
      internalID: -2075700440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_19
      rect:
        serializedVersion: 2
        x: 170
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 21be8fa971540cb40845b404e94a40d0
      internalID: -97482981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_20
      rect:
        serializedVersion: 2
        x: 340
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 28abf1e2c63fbd94ba22ffd5f0457852
      internalID: 2075455838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_21
      rect:
        serializedVersion: 2
        x: 510
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9a9380e3443cf334c8a384ebeae912da
      internalID: 252269555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_22
      rect:
        serializedVersion: 2
        x: 680
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9b9bc9dbba213443ad22d85a53aeb50
      internalID: 1132648053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_23
      rect:
        serializedVersion: 2
        x: 850
        y: 343.3333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a013f35eae0ba594590b9079ba44af61
      internalID: 946506514
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_24
      rect:
        serializedVersion: 2
        x: 0
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ad25729f7e3d604e97c7bc82e7656ec
      internalID: -1325763451
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_25
      rect:
        serializedVersion: 2
        x: 170
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 417a41b8417cfe8459f5ff29ae317ebd
      internalID: -1070681365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_26
      rect:
        serializedVersion: 2
        x: 340
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 42ef22054783e494e9899fe48fc8a26d
      internalID: 235405150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_27
      rect:
        serializedVersion: 2
        x: 510
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: caa0ff4c0db66e44aa4b4cbff9fe4339
      internalID: 1339639361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_28
      rect:
        serializedVersion: 2
        x: 680
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 04daaf0945fa99b4eb3d42bb080501d9
      internalID: -330243598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_29
      rect:
        serializedVersion: 2
        x: 850
        y: 173.33333
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c984ba13eca4e8648b3ea0fc64d848bc
      internalID: 1496082012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_30
      rect:
        serializedVersion: 2
        x: 0
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9ba7cfa4a7e75647b5f0aaa87fca5d9
      internalID: -807118924
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_31
      rect:
        serializedVersion: 2
        x: 170
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fb208064776f9b341b68db2fa5561396
      internalID: 1834100187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_32
      rect:
        serializedVersion: 2
        x: 340
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 22412c2cf32b4d1469a2bd402e574283
      internalID: 870091401
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_33
      rect:
        serializedVersion: 2
        x: 510
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd073accd5ca78c42be632a22cbdb09c
      internalID: -329014271
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_34
      rect:
        serializedVersion: 2
        x: 680
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd8f3ccf796f64d44bd57ecbcfdcb175
      internalID: -1037737805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: theme4_35
      rect:
        serializedVersion: 2
        x: 850
        y: 3.3333282
        width: 170.66667
        height: 170.66667
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 31b7f3ff8ccd05e4ab8d13c31151cc46
      internalID: 1665405284
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: c31a43c8e8020794d98eb4c502d64ead
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      theme4_4: 1954813571
      theme4_13: -1678766380
      theme4_2: -1491330017
      theme4_29: 1496082012
      theme4_19: -97482981
      theme4_20: 2075455838
      theme4_8: 302751509
      theme4_22: 1132648053
      theme4_6: 1520430069
      theme4_12: -1046997238
      theme4_23: 946506514
      theme4_1: 159631622
      theme4_35: 1665405284
      theme4_10: -418266704
      theme4_24: -1325763451
      theme4_27: 1339639361
      theme4_14: -1739448068
      theme4_17: -2060796729
      theme4_26: 235405150
      theme4_30: -807118924
      theme4_31: 1834100187
      theme4_9: -139452264
      theme4_11: -1175288546
      theme4_32: 870091401
      theme4_28: -330243598
      theme4_21: 252269555
      theme4_7: -810876210
      theme4_25: -1070681365
      theme4_34: -1037737805
      theme4_18: -2075700440
      theme4_3: 1586977058
      theme4_0: -1214504659
      theme4_5: 237620543
      theme4_16: 372247162
      theme4_15: -1572735854
      theme4_33: -329014271
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
